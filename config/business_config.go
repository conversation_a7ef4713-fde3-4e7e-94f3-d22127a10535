package config

import (
	"encoding/json"
	"git.yy.com/golang/yylog"
	"git.yy.com/server/jiaoyou/go_projects/api/common/env"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
	"go.uber.org/zap"
)

// 业务配置
type businessConfig struct {
	MigrateHost                string        `json:"migrate_host"`                 // 迁移的 host 区分环境 如 https://hd-test.yy.com
	MigratedTabIDs             []int64       `json:"migrated_tab_ids"`             // 已迁移的tab id
	MigratedTerminalCategories map[int][]int `json:"migrated_terminal_categories"` // 已迁移的终端分类 map[terminal]list[category]
	MigrateGetTopInfo          bool          `json:"migrate_get_top_info"`         // 是否迁移 /zhuiya_recommend/v2/get_top_info
	MigrateGetBannerInfo       bool          `json:"migrate_get_banner_info"`      // 是否迁移 /zhuiya_recommend/v2/get_banner_info
	MigrateGetTabInfo          bool          `json:"migrate_get_tab_info"`         // 是否迁移 /zhuiya_recommend/v2/get_tab_info
	MigrateGetRecommendInfo    bool          `json:"migrate_get_recommend_info"`   // 是否迁移 /zhuiya_recommend/v2/get_recommend_info
}

// IsMigratedTabID 是否已经迁移了推荐tabId
func (b *businessConfig) IsMigratedTabID(id int64) bool {
	if len(b.MigratedTabIDs) == 0 {
		return false
	}
	for _, migratedTabID := range b.MigratedTabIDs {
		if id == migratedTabID {
			return true
		}
	}
	return false
}

// IsMigratedTerminalCategory 是否已经迁移了
func (b *businessConfig) IsMigratedTerminalCategory(terminal, category int) bool {
	if len(b.MigratedTerminalCategories) == 0 {
		return false
	}
	if categories, ok := b.MigratedTerminalCategories[terminal]; ok {
		for _, c := range categories {
			if c == category {
				return true
			}
		}
	}
	return false
}

// Business 业务配置
var Business businessConfig

// 业务配置
func loadBusinessConfig(isTest bool) {
	metaServerURL := "nbcfg-pro-in.yy.com"
	if isTest {
		if env.GetUnitTest() {
			metaServerURL = "nbcfg-dev.yy.com"
		} else {
			metaServerURL = "nbcfg-dev-in.yy.com"
		}
	}
	if len(ServerConfig.ApolloMetaURL) > 0 {
		metaServerURL = ServerConfig.ApolloMetaURL
	}
	appID := "b2e1e5eb2daf9bd01c4efd6cf3cd9180"
	var at = fts.GetApolloTools()
	// 业务配置
	at.HandleFunc("business_config.json", "", func(Value string, oldValue string) (err error) {
		var tempBusiness businessConfig
		if len(Value) > 0 {
			err = json.Unmarshal([]byte(Value), &tempBusiness)
			if err != nil {
				yylog.ErrorWithBam("json.Unmarshal",
					zap.Error(err),
					zap.String("value", Value),
				)
				return err
			}
			yylog.Info("updateBusinessConfig", zap.Any("businessConfig", tempBusiness), zap.String("value", Value))
		}
		Business = tempBusiness
		return err
	})
	err := at.ListenAndServer(metaServerURL, appID, true)
	if err != nil {
		panic(err)
	}
	yylog.Info("", zap.Any("businessConfig", Business))
}
