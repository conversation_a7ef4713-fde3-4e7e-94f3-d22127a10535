package v2

import (
	"fmt"
	"strconv"
	"strings"

	"git.yy.com/golang/gfy/v2/yy/yylog"
	"go.uber.org/zap"

	"git.yy.com/server/jiaoyou/go_projects/api/common/alert"
	"github.com/globalsign/mgo"
	"github.com/globalsign/mgo/bson"
)

// RecommendManageConfig 推荐管理配置
type RecommendManageConfig struct {
	ID                 string  `bson:"_id" json:"id"`
	Name               string  `bson:"name" json:"name"`                                                                             // 名称
	Category           int     `bson:"category" json:"category"`                                                                     // 对应导航配置的Category
	ZoneID             int64   `bson:"zoneid" json:"zoneid" binding:"gt=0"`                                                          // 由导航管理配置, tab id
	Regular            string  `bson:"regular" json:"regular"`                                                                       // 取数规则
	LabelStatus        int     `bson:"label_status,omitempty" json:"label_status,omitempty"`                                         // 标签状态 1-显示 2-不显示
	AttributesStatus   int     `bson:"attributes_status" json:"attributes_status" binding:"gte=1,lte=2"`                             // 属性状态 1-显示 2-不显示
	ConfigMode         int     `bson:"config_mode" json:"config_mode" binding:"gte=1,lte=2"`                                         // 配置方式 1-运营配置 2-自动推荐
	PositionList       []int64 `bson:"position_list,omitempty" json:"position_list,omitempty"`                                       // 运营配置：推荐位置
	Status             int     `bson:"status,omitempty" json:"status,omitempty"`                                                     // 推荐状态 1：推荐、2：不推荐
	RecommendStartTime int64   `bson:"recommend_start,omitempty" json:"recommend_start,omitempty" binding:"gte=0"`                   // 运营配置：推荐开始的时间
	RecommendEndTime   int64   `bson:"recommend_end,omitempty" json:"recommend_end,omitempty" binding:"gtefield=RecommendStartTime"` // 运营配置：推荐结束的时间
	Terminal           int     `bson:"terminal" json:"terminal" binding:"gte=1,lte=5"`                                               // 推荐终端：1-追玩APP、2-PC导流专区
	Sort               int     `bson:"sort" json:"sort" binding:"gte=0,lte=1"`
}

// RecommendSinkZoneInfo 沉底信息
type RecommendSinkZoneInfo struct {
	Platform   int    `bson:"platform" json:"platform"`     // 平台 1: android、2: ios、3: android和ios
	Terminal   int    `bson:"terminal" json:"terminal"`     // 推荐终端：1-追玩APP、2-PC导流专区
	Category   int    `bson:"category" json:"category"`     // 1-娱乐、2游戏
	ZoneID     int64  `bson:"zoneId" json:"zoneId"`         // 由导航管理配置，tab id
	Title      string `bson:"title" json:"title"`           // 标题
	SinkMode   int    `bson:"sinkMode" json:"sinkMode"`     // 沉底类型 1-厅 2-主播
	UID        int64  `bson:"uid" json:"uid"`               // 沉底类型为1时，对应ow uid；为2时，对应主播uid
	SID        int64  `bson:"sid" json:"sid"`               // 频道
	SSID       int64  `bson:"ssid" json:"ssid"`             // 子频道
	PunishType int    `bson:"punishType" json:"punishType"` // 处罚类型 0-系统处罚 1-人工处罚
	Timestamp  int64  `bson:"timestamp" json:"timestamp"`   // 沉底生成时间
}

// RecommendSinkZoneConfig 推荐管理-沉底区域 人工导入配置
type RecommendSinkZoneConfig struct {
	ID                    string `bson:"_id" json:"id"`
	Name                  string `bson:"name" json:"name"`           // 名称
	StartTime             int64  `bson:"startTime" json:"startTime"` // 运营配置：沉底开始的时间
	EndTime               int64  `bson:"endTime" json:"endTime"`     // 运营配置：沉底结束的时间
	SinkConfigID          string `bson:"-" json:"sinkConfigId"`
	RecommendSinkZoneInfo `bson:",inline"`
}

// RecommendSinkZoneDetail 推荐管理-沉底区域 实时推荐列表
type RecommendSinkZoneDetail struct {
	SinkConfigID          string `bson:"sinkConfigId"`
	SinkReason            string `bson:"sinkReason"` // 沉底原因
	RecommendSinkZoneInfo `bson:",inline"`
}

// Module 模块插入相关的信息
type Module struct {
	ID       int64  `bson:"id" json:"id"`     // 模块关联的tab id
	Name     string `bson:"name" json:"name"` // 模块关联的tab name
	Category int    `bson:"category" json:"category"`
	Position int    `bson:"position" json:"position"` // 插入的位置
}

// RecommendTabConfig 导航配置
type RecommendTabConfig struct {
	ID                      int64  `bson:"_id" json:"id"`
	Category                int    `bson:"category" json:"category"`                                 // 1-娱乐、2-游戏、3-发现页、4-分流推荐 andMore...
	Platform                int    `bson:"platform" json:"platform"`                                 // 平台 1: android、2: ios、3: android和ios
	Name                    string `bson:"name" json:"name"`                                         // 名称
	Weight                  int    `bson:"weight" json:"weight" binding:"gte=1,lte=100"`             // 权重
	Show                    int    `bson:"show" json:"show" binding:"gte=1,lte=2"`                   // 推荐模式：1-外显、2-不外显
	Logic                   int    `bson:"logic" json:"logic"`                                       // 推荐逻辑
	MinSize                 int    `bson:"min_size" json:"min_size"`                                 // 整个tab最小推荐的数量，小于这个数量则会触发告警
	Style                   int    `bson:"style" json:"style" binding:"omitempty,gt=0,lte=5"`        // 游戏Tab样式：0-默认、1-大图样式
	Terminal                int    `bson:"terminal" json:"terminal"`                                 // 推荐终端：1-追玩APP、2-PC导流专区、3-YO语音
	GameType                []int  `bson:"gameType" json:"gameType"`                                 // 推荐玩法
	GameIDList              []int  `bson:"gameId" json:"-"`                                          // 云游戏gameId
	PopularityValueMin      int64  `bson:"popularityValueMin" json:"popularityValueMin"`             // 人气值低于X时，推荐沉底 <=0时不判断 人气值低于X时，推荐沉底
	Afk                     bool   `bson:"afk" json:"afk"`                                           // 低质量开播
	Unpopular               bool   `bson:"unpopular" json:"unpopular"`                               // 不受欢迎
	CheckBlackList          bool   `bson:"check_black_list" json:"checkBlackList"`                   // 是否拦截黑名单
	CheckProbabilityPlay    bool   `bson:"check_probability_play" json:"checkProbabilityPlay"`       // 概率玩法
	StartVersion            string `bson:"start_version" json:"startVersion,omitempty"`              // 开始版本号
	EndVersion              string `bson:"end_version" json:"endVersion,omitempty"`                  // 结束版本号
	VersionSelect           int    `bson:"version_select" json:"versionSelect,omitempty"`            // 0-全部、1-部分显示、2-部分不显示
	SupplementRecommendType int    `bson:"supplement_recommend_type" json:"supplementRecommendType"` // 补量推荐类型-数据库定义
	AllowMissCover          int    `bson:"allow_miss_cover" json:"allowMissCover"`                   // 是否允许缺失推荐图，1-允许，0-不允许
	RoomType                []int  `bson:"room_type" json:"roomType"`                                // 房间类型
	ShowMinSize             int    `bson:"show_min_size" json:"showMinSize"`                         // 少于X个推荐房间时不显示tab
	InsertedTab             Module `bson:"module" json:"module"`                                     // 模块插入相关
}

// RecommendItem 推荐列表
type RecommendItem struct {
	ID                 string `bson:"_id" json:"id"`
	UID                int64  `bson:"uid,omitempty" json:"uid,omitempty"`
	Sid                int64  `bson:"sid,omitempty" json:"sid,omitempty"`
	Ssid               int64  `bson:"ssid,omitempty" json:"ssid,omitempty"`
	ZoneID             int64  `bson:"zoneid" json:"zoneid" binding:"gt=0"`                                                          // 推荐位置
	ConfZoneID         int64  `bson:"confZoneID" json:"confZoneID"`                                                                 // 原始配置zoneid
	Weight             int64  `bson:"weight" json:"weight"`                                                                         // 权重
	Business           int    `bson:"business" json:"business"`                                                                     // 业务 1: 交友、2：陪玩
	Platform           int    `bson:"platform" json:"platform"`                                                                     // 推荐平台
	Position           int64  `bson:"position,omitempty" json:"position,omitempty"`                                                 // 运营配置：推荐位置
	AttributesStatus   int    `bson:"attributes_status" json:"attributes_status" binding:"gte=0,lte=2"`                             // 属性状态 1-显示 2-不显示
	RecommendStartTime int64  `bson:"recommend_start,omitempty" json:"recommend_start,omitempty" binding:"gte=0"`                   // 运营配置：推荐开始的时间
	RecommendEndTime   int64  `bson:"recommend_end,omitempty" json:"recommend_end,omitempty" binding:"gtefield=RecommendStartTime"` // 运营配置：推荐结束的时间
	LabelStartTime     int64  `bson:"label_start,omitempty" json:"label_start,omitempty"`                                           // 运营配置：推荐开始的时间
	LabelEndTime       int64  `bson:"label_end,omitempty" json:"label_end,omitempty"`                                               // 运营配置：推荐结束的时间
	Mode               int    `bson:"mode" json:"mode" binding:"gte=1,lte=2"`                                                       // 推荐模式：1-按厅推荐、2-按uid推荐
	RecommendStatus    int    `bson:"recommend_status" json:"recommend_status" binding:"gte=1,lte=2"`                               // 推荐状态
	Terminal           int    `bson:"terminal" json:"terminal" binding:"gte=1,lte=5"`                                               // 推荐终端：0-追玩APP、1-PC导流专区
	RecommendManageID  string `bson:"recommend_manage_id" json:"recommend_manage_id"`                                               // 推荐管理配置id
}

// FormulaParameter 公式参数
type FormulaParameter struct {
	ID     string `bson:"_id" json:"id"`
	ZoneID int    `bson:"zoneid" json:"zoneid"`
	X1     int    `bson:"x1" json:"x1"`
	X2     int    `bson:"x2" json:"x2"`
	Y1     int    `bson:"y1" json:"y1"`
	Y2     int    `bson:"y2" json:"y2"`
	Y3     int    `bson:"y3" json:"y3"`
	Y4     int    `bson:"y4" json:"y4"`
	Y5     int    `bson:"y5" json:"y5"`
	Y6     int    `bson:"y6" json:"y6"`
}

// OpPopularityValueConf 运营人气配置
type OpPopularityValueConf struct {
	ID        string `bson:"_id" json:"id"`
	Sid       int64  `bson:"sid" json:"sid"`
	Ssid      int64  `bson:"ssid" json:"ssid"`
	StartTime string `bson:"startTime" json:"startTime"`
	EndTime   string `bson:"endTime" json:"endTime"`
	PerSecond int    `bson:"perSecond" json:"perSecond"`
	Value     int    `bson:"value" json:"value"`
}

// RecommendBlackList 推荐黑名单
type RecommendBlackList struct {
	ID       string `bson:"_id" json:"id"`
	YY       int64  `bson:"-" json:"yy,omitempty"`
	UID      int64  `bson:"uid,omitempty" json:"uid,omitempty"`
	Sid      int64  `bson:"sid,omitempty" json:"sid,omitempty"`
	Ssid     int64  `bson:"ssid,omitempty" json:"ssid,omitempty"`
	ASID     int64  `bson:"-" json:"asid,omitempty"`
	Type     int    `bson:"type" json:"type"`
	Kind     int    `bson:"kind" json:"-"` // 0-普通黑名单 1-帽子超主
	Remark   string `bson:"remark,omitempty" json:"remark"`
	Creator  string `bson:"creator,omitempty" json:"creator,omitempty"`
	CreateAt int64  `bson:"createAt,omitempty" json:"createAt,omitempty"`
}

const (
	recommendChannel = 1 // 按厅推荐
	recommendCompere = 2 // 按主持推荐

	statusRecommended    = 1 // 推荐
	statusNotRecommended = 2 // 未推荐

	configModeAudit         = 1 // 运营配置
	configModeAutoRecommend = 2 // 自动推荐

	show    = 1 // 显示
	notShow = 2 // 不显示

	// SidBlackList sid 黑名单
	SidBlackList = 1
	// ChannelBlackList 频道 黑名单
	ChannelBlackList = 2
	// UIDBlackList uid 黑名单
	UIDBlackList = 3

	allDisplay        = 0 // 全部
	partialDisplay    = 1 // 部分显示
	partialNotDisplay = 2 // 部分不显示
)

const (
	// BlackKindNormal 0-普通黑名单
	BlackKindNormal = iota
	// BlackKindHatking 1-帽子超主
	BlackKindHatking
)

// IsValidShowStatus 显示、不显示
func (r *RecommendTabConfig) IsValidShowStatus() bool {
	if r == nil {
		return false
	}
	return r.Show == show || r.Show == notShow
}

// IsValidStyle 样式
func (r *RecommendTabConfig) IsValidStyle() bool {
	if r == nil {
		return false
	}
	return r.Style >= 0 && r.Style <= 4
}

// IsValidVersionSelect 版本控制
func (r *RecommendTabConfig) IsValidVersionSelect() bool {
	if r == nil {
		return false
	}
	return r.VersionSelect == allDisplay || r.VersionSelect == partialDisplay || r.VersionSelect == partialNotDisplay
}

func getVersion(version string) (major, minor, patch int) {
	s := strings.Split(version, ".")
	if len(s) != 3 {
		return
	}
	major, _ = strconv.Atoi(s[0])
	minor, _ = strconv.Atoi(s[1])
	patch, _ = strconv.Atoi(s[2])
	return
}

func toVersion(major, minor, patch int) int64 {
	return int64(major)<<32 | int64(minor)<<16 | int64(patch)
}

// StartVersionLessThanEndVersion 开始版本小于结束版本
func (r *RecommendTabConfig) StartVersionLessThanEndVersion() bool {
	if len(r.StartVersion) == 0 || len(r.EndVersion) == 0 {
		return true
	}
	return toVersion(getVersion(r.StartVersion)) < toVersion(getVersion(r.EndVersion))
}

// ShowThisVersion 对应tab当前版本是否显示
func (r *RecommendTabConfig) ShowThisVersion(major, minor, patch int) bool {
	if r.VersionSelect == allDisplay {
		return true
	}
	gteStartVersion := true
	if len(r.StartVersion) > 0 {
		gteStartVersion = toVersion(major, minor, patch) >= toVersion(getVersion(r.StartVersion))
	}
	lteEndVersion := true
	if len(r.EndVersion) > 0 {
		lteEndVersion = toVersion(getVersion(r.EndVersion)) >= toVersion(major, minor, patch)
	}
	if r.VersionSelect == partialDisplay {
		return gteStartVersion && lteEndVersion
	} else if r.VersionSelect == partialNotDisplay {
		return !(gteStartVersion && lteEndVersion)
	}
	return true
}

// IsShowStatus 显示
func (r *RecommendTabConfig) IsShowStatus() bool {
	if r == nil {
		return false
	}
	return r.Show == show
}

// IsRecommendGameID 云游戏推荐gameId
func (r *RecommendTabConfig) IsRecommendGameID(id int) bool {
	for _, gameID := range r.GameIDList {
		if id == gameID {
			return true
		}
	}
	return false
}

// IsValidLabelStatus 显示、不显示
func (r *RecommendManageConfig) IsValidLabelStatus() bool {
	if r == nil {
		return false
	}
	return r.LabelStatus == show || r.LabelStatus == notShow
}

// IsShowLabelStatus 显示
func (r *RecommendManageConfig) IsShowLabelStatus() bool {
	if r == nil {
		return false
	}
	return r.LabelStatus == show
}

// IsValidAttributesStatus 显示、不显示
func (r *RecommendManageConfig) IsValidAttributesStatus() bool {
	if r == nil {
		return false
	}
	return r.AttributesStatus == show || r.AttributesStatus == notShow
}

// IsShowAttributesStatus 显示
func (r *RecommendManageConfig) IsShowAttributesStatus() bool {
	if r == nil {
		return false
	}
	return r.AttributesStatus == show
}

// IsAuditConfigMode 运营配置tab
func (r *RecommendManageConfig) IsAuditConfigMode() bool {
	if r == nil {
		return false
	}
	return r.ConfigMode == configModeAudit
}

// IsAutoRecommendConfigMode 自动推荐tab
func (r *RecommendManageConfig) IsAutoRecommendConfigMode() bool {
	if r == nil {
		return false
	}
	return r.ConfigMode == configModeAutoRecommend
}

// IsAutoRecommendConfigMode 自动推荐tab
func IsAutoRecommendConfigMode(mode int) bool {
	return mode == configModeAutoRecommend
}

// IsValidConfigMode 运营配置、自动推荐tab
func (r *RecommendManageConfig) IsValidConfigMode() bool {
	if r == nil {
		return false
	}
	return r.ConfigMode == configModeAudit || r.ConfigMode == configModeAutoRecommend
}

// IsValidRecommendStatus 推荐、未推荐
func (r *RecommendManageConfig) IsValidRecommendStatus() bool {
	if r == nil {
		return false
	}
	return r.Status == statusRecommended || r.Status == statusNotRecommended
}

// IsRecommendStatus 推荐
func (r *RecommendManageConfig) IsRecommendStatus() bool {
	if r == nil {
		return false
	}
	return r.Status == statusRecommended
}

// RecommendTimeExpired 推荐时间过期
func (r *RecommendManageConfig) RecommendTimeExpired(t int64) bool {
	return t < r.RecommendStartTime || t > r.RecommendEndTime
}

// isEffective 沉底区域人工导入数据是否生效
func (r RecommendSinkZoneConfig) isEffective(ts int64) bool {
	return r.PunishType == 1 && ts >= r.StartTime && ts <= r.EndTime
}

// IsRecommendCompere 按uid推荐
func (r *RecommendItem) IsRecommendCompere() bool {
	if r == nil {
		return false
	}
	return r.Mode == recommendCompere
}

// IsRecommendChannel 按厅推荐
func (r *RecommendItem) IsRecommendChannel() bool {
	if r == nil {
		return false
	}
	return r.Mode == recommendChannel
}

// IsRecommendChannel 按厅推荐
func IsRecommendChannel(mode int) bool {
	return mode == recommendChannel
}

// IsRecommendCompere 按主持推荐
func IsRecommendCompere(mode int) bool {
	return mode == recommendCompere
}

// IsValidRecommendMode 按厅推荐、按人推荐
func (r *RecommendItem) IsValidRecommendMode() bool {
	if r == nil {
		return false
	}
	return r.Mode == recommendChannel || r.Mode == recommendCompere
}

// IsValidAttributesStatus 显示、不显示
func (r *RecommendItem) IsValidAttributesStatus() bool {
	if r == nil {
		return false
	}
	return r.AttributesStatus == show || r.AttributesStatus == notShow
}

// IsShowAttributesStatus 显示
func (r *RecommendItem) IsShowAttributesStatus() bool {
	if r == nil {
		return false
	}
	if r.AttributesStatus == 0 {
		return true
	}
	return r.AttributesStatus == show
}

// LabelExpired 标签是否过期
func (r *RecommendItem) LabelExpired(t int64) bool {
	if r.LabelStartTime == 0 || r.LabelEndTime == 0 {
		return false
	}
	return !(t >= r.LabelStartTime && t <= r.LabelEndTime)
}

// RecommendTimeExpired 推荐时间过期
func (r *RecommendItem) RecommendTimeExpired(t int64) bool {
	if r.RecommendStartTime == 0 || r.RecommendEndTime == 0 {
		return false
	}
	return !(t >= r.RecommendStartTime && t <= r.RecommendEndTime)
}

// IsRecommendStatus 推荐
func (r *RecommendItem) IsRecommendStatus() bool {
	if r == nil {
		return false
	}
	return r.RecommendStatus == statusRecommended
}

// IsValidRecommendStatus 推荐、不推荐
func (r *RecommendItem) IsValidRecommendStatus() bool {
	if r == nil {
		return false
	}
	return r.RecommendStatus == statusRecommended || r.RecommendStatus == statusNotRecommended
}

// IsRecommendBlackListType 黑名单类型
func (r *RecommendBlackList) IsRecommendBlackListType() bool {
	if r == nil {
		return false
	}
	return r.Type == SidBlackList || r.Type == ChannelBlackList || r.Type == UIDBlackList
}

// IsSidBlackListType  sid 黑名单
func (r *RecommendBlackList) IsSidBlackListType() bool {
	if r == nil {
		return false
	}
	return r.Type == SidBlackList
}

// IsChannelBlackList  channel 黑名单
func (r *RecommendBlackList) IsChannelBlackList() bool {
	if r == nil {
		return false
	}
	return r.Type == ChannelBlackList
}

// IsUIDBlackListType  sid 黑名单
func (r *RecommendBlackList) IsUIDBlackListType() bool {
	if r == nil {
		return false
	}
	return r.Type == UIDBlackList
}

// IsNormalKind 是否普通黑名单
func (r *RecommendBlackList) IsNormalKind() bool {
	if r == nil {
		return false
	}
	return r.Kind == BlackKindNormal
}

// IsHatkingKind 是否帽子超主黑名单
func (r *RecommendBlackList) IsHatkingKind() bool {
	if r == nil {
		return false
	}
	return r.Kind == BlackKindHatking
}

// GetRecommendManageConfig 获取推荐管理配置
func GetRecommendManageConfig(terminal, category, configMode int, zoneID int64) (configs []RecommendManageConfig, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{}
	if terminal > 0 {
		query["terminal"] = terminal
	}
	if configMode > 0 {
		query["config_mode"] = configMode
	}
	if category > 0 {
		query["category"] = category
	}
	if zoneID > 0 {
		query["zoneid"] = zoneID
	}

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendManage)
	iter := client.Find(query).Iter()
	var config RecommendManageConfig
	for iter.Next(&config) {
		configs = append(configs, config)
	}
	yylog.Info("get recommend manage config", zap.Any("query", query), zap.Int("len(configs)", len(configs)), zap.Error(err))
	return
}

// GetAllRecommendManageMap 获取推荐管理配置
func GetAllRecommendManageMap() (configs map[string]RecommendManageConfig, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendManage)
	iter := client.Find(bson.M{}).Iter()
	var config RecommendManageConfig
	for iter.Next(&config) {
		if configs == nil {
			configs = make(map[string]RecommendManageConfig)
		}
		configs[config.ID] = config
	}
	yylog.Info("get recommend manage config", zap.Int("len(configs)", len(configs)), zap.Error(err))
	return
}

// GetRecommendManageConfigByID 获取推荐管理配置
func GetRecommendManageConfigByID(id string) (config RecommendManageConfig, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendManage)
	err = client.FindId(id).One(&config)
	yylog.Info("get recommend manage config by id", zap.Any("config", config), zap.Error(err))
	return
}

// AddRecommendManageConfig 添加推荐管理配置
func AddRecommendManageConfig(p *RecommendManageConfig) (isDup bool, id string, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	id = bson.NewObjectId().Hex()
	p.ID = id
	client := session.DB(ftsZhuiYaRecommendDB).C(recommendManage)
	err = client.Insert(p)
	if mgo.IsDup(err) {
		isDup = true
		err = nil
	}
	yylog.Info("add recommend manage config", zap.Any("p", p), zap.Bool("isDup", isDup), zap.Error(err))
	return
}

// UpdateRecommendManageConfig 更新推荐管理配置
func UpdateRecommendManageConfig(p *RecommendManageConfig) (err error) {
	if p == nil {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendManage)
	err = client.Update(bson.M{"_id": p.ID}, bson.M{"$set": p})
	yylog.Info("update recommend manage config", zap.Any("p", p), zap.Error(err))
	return
}

// RemoveRecommendManageConfig 删除推荐管理配置
func RemoveRecommendManageConfig(id string) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendManage)

	err = client.RemoveId(id)
	if err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("remove recommend manage config", zap.String("id", id), zap.Error(err))
	return
}

// AddRecommendItem 添加推荐信息
func AddRecommendItem(p *RecommendItem) (isDup bool, err error) {
	if p == nil {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)
	err = client.Insert(p)
	if mgo.IsDup(err) {
		isDup = true
		err = nil
	}
	yylog.Info("add recommend item", zap.Any("p", p), zap.Bool("isDup", isDup), zap.Error(err))
	return
}

// GetRecommendItem 推荐信息
func GetRecommendItem(recommendManageID string, mode, business int) (items []RecommendItem, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{}
	if len(recommendManageID) > 0 {
		query["recommend_manage_id"] = recommendManageID
	}
	if mode > 0 {
		query["mode"] = mode
	}
	if business > 0 {
		query["business"] = business
	}

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)
	iter := client.Find(query).Iter()
	var item RecommendItem
	for iter.Next(&item) {
		items = append(items, item)
	}
	yylog.Info("get recommend item", zap.Any("query", query), zap.Int("len(items)", len(items)), zap.Error(err))
	return
}

// GetRecommendItemByTabID 推荐信息
func GetRecommendItemByTabID(tabID, tabStep int64, mode, business int) (items []RecommendItem, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{}
	if tabID > 0 {
		query["zoneid"] = bson.M{"$gte": tabID, "$lt": tabID + tabStep}
	}
	if mode > 0 {
		query["mode"] = mode
	}
	if business > 0 {
		query["business"] = business
	}

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)
	iter := client.Find(query).Iter()
	var item RecommendItem
	for iter.Next(&item) {
		items = append(items, item)
	}
	yylog.Info("", zap.Any("query", query), zap.Int("len(items)", len(items)), zap.Error(err))
	return
}

// UpdateRecommendItem 更新推荐信息
func UpdateRecommendItem(p *RecommendItem) (err error) {
	if p == nil {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)
	err = client.Update(bson.M{"_id": p.ID}, bson.M{"$set": p})
	yylog.Info("update recommend item", zap.Any("p", p), zap.Error(err))
	return
}

// UpdateRecommendItemStatus 更新推荐状态
func UpdateRecommendItemStatus(id string, status int) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)
	err = client.Update(bson.M{"_id": id}, bson.M{"$set": bson.M{"recommend_status": status}})
	yylog.Info("update recommend item status", zap.String("id", id), zap.Int("status", status), zap.Error(err))
	return
}

// RemoveRecommendItem 删除推荐信息
func RemoveRecommendItem(ids []string) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)

	_, err = client.RemoveAll(bson.M{"_id": bson.M{"$in": ids}})
	if err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("remove recommend item", zap.Strings("id", ids), zap.Error(err))
	return
}

// RemoveRecommendItemByManageID 删除对应推荐位的信息
func RemoveRecommendItemByManageID(id string) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)

	_, err = client.RemoveAll(bson.M{"recommend_manage_id": id})
	if err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("remove recommend item by manage id", zap.String("id", id), zap.Error(err))
	return
}

// RemoveRecommendItemByZoneID 删除对应推荐位的信息
func RemoveRecommendItemByZoneID(idList []int64) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)

	_, err = client.RemoveAll(bson.M{"zoneid": bson.M{"$in": idList}})
	if err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("remove recommend item by zone id", zap.Int64s("id", idList), zap.Error(err))
	return
}

// BatchUpdateRecommendItem 批量更新推荐信息
func BatchUpdateRecommendItem(items []RecommendItem) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	const n = 200
	client := session.DB(ftsZhuiYaRecommendDB).C(recommendItemConfig)
	bulk := client.Bulk()
	for len(items) > n {
		for _, item := range items[:n] {
			bulk.Upsert(bson.M{"_id": item.ID}, bson.M{"$set": item})
			yylog.Info("", zap.Any("item", item))
		}
		_, err = bulk.Run()
		if err != nil {
			return
		}
		items = items[n:]
	}
	if len(items) > 0 {
		for _, item := range items {
			bulk.Upsert(bson.M{"_id": item.ID}, bson.M{"$set": item})
			yylog.Info("", zap.Any("item", item))
		}
		_, err = bulk.Run()
		if err != nil {
			return
		}
	}
	yylog.Info("batch update recommend item", zap.Any("len(items)", len(items)), zap.Error(err))
	return
}

// GetRecommendTabConfig 导航配置
func GetRecommendTabConfig(terminal, category, platform int) (configs []RecommendTabConfig, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{}
	if terminal > 0 {
		query["terminal"] = terminal
	}
	if category > 0 {
		query["category"] = category
	}
	if platform > 0 {
		query["platform"] = platform
	}
	client := session.DB(ftsZhuiYaRecommendDB).C(recommendTabConfig)
	iter := client.Find(query).Iter()
	var config RecommendTabConfig
	for iter.Next(&config) {
		configs = append(configs, config)
	}
	yylog.Info("get recommend tab config", zap.Any("query", query), zap.Int("len(configs)", len(configs)), zap.Error(err))
	return
}

// GetRecommendTabConfigByID 导航配置
func GetRecommendTabConfigByID(id int64) (config RecommendTabConfig, err error) {
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendTabConfig)
	err = client.Find(bson.M{"_id": id}).One(&config)
	yylog.Info("get recommend tab config by id", zap.Int64("id", id), zap.Any("config", config), zap.Error(err))
	return
}

// UpdateRecommendTabConfig 更新导航配置
func UpdateRecommendTabConfig(p *RecommendTabConfig) (err error) {
	if p == nil {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendTabConfig)
	err = client.Update(bson.M{"_id": p.ID}, bson.M{"$set": p})
	yylog.Info("update recommend tab config", zap.Any("p", p), zap.Error(err))
	return
}

// FindRecommendTabConfigUsedID 导航配置已使用的ID
func FindRecommendTabConfigUsedID(terminal int) (used map[int64]bool, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendTabConfig)
	iter := client.Find(bson.M{"terminal": terminal}).Iter()

	var config RecommendTabConfig
	for iter.Next(&config) {
		if used == nil {
			used = make(map[int64]bool)
		}
		used[config.ID] = true
	}
	yylog.Info("find recommend tab config use id", zap.Int("terminal", terminal), zap.Any("config", config), zap.Int("len(used)", len(used)), zap.Error(err))
	return
}

// AddRecommendTabConfig 添加导航配置
func AddRecommendTabConfig(p *RecommendTabConfig) (isDup bool, err error) {
	if p == nil {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendTabConfig)
	err = client.Insert(p)
	if mgo.IsDup(err) {
		isDup = true
		err = nil
	}
	yylog.Info("add recommend tab config", zap.Any("p", p), zap.Bool("isDup", isDup), zap.Error(err))
	return
}

// RemoveRecommendTabConfig 删除导航配置
func RemoveRecommendTabConfig(id int64) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendTabConfig)

	err = client.RemoveId(id)
	if err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("remove recommend tab config", zap.Int64("id", id), zap.Error(err))
	return
}

// GetFormulaParameter 获取公式参数
func GetFormulaParameter(zoneID int) (pram FormulaParameter, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	// 初始化为1
	pram.ZoneID = zoneID
	pram.X1 = 1
	pram.X2 = 1
	pram.Y1 = 1
	pram.Y2 = 1
	pram.Y3 = 1
	pram.Y4 = 1
	pram.Y5 = 1
	pram.Y6 = 1

	query := bson.M{}
	if zoneID > 0 {
		query["zoneid"] = zoneID
	}

	client := session.DB(ftsZhuiYaRecommendDB).C(formulaParameter)
	iter := client.Find(query).Iter()

	for iter.Next(&pram) {
		break
	}
	yylog.Info("", zap.Any("query", query), zap.Any("pram", pram), zap.Error(err))
	return
}

// SetFormulaParameter 设置公式参数
func SetFormulaParameter(pram FormulaParameter) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(formulaParameter)
	err = client.Update(bson.M{"zoneid": pram.ZoneID},
		bson.M{"$set": bson.M{"x1": pram.X1, "x2": pram.X2, "y1": pram.Y1,
			"y2": pram.Y2, "y3": pram.Y3, "y4": pram.Y4, "y5": pram.Y5, "y6": pram.Y6}})
	if err == mgo.ErrNotFound {
		err = nil

		err = client.Insert(pram)
		if mgo.IsDup(err) {
			err = nil
		}
	}

	yylog.Info("", zap.Any("pram", pram), zap.Error(err))
	return
}

// RemoveFormulaByZoneID 删除对应公式
func RemoveFormulaByZoneID(zoneID int64) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(formulaParameter)

	_, err = client.RemoveAll(bson.M{"zoneid": zoneID})
	if err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("", zap.Int64("zoneId", zoneID), zap.Error(err))
	return
}

// GetOpPopularityValueConf 查询运营人气配置
func GetOpPopularityValueConf(sid int64) (confs []OpPopularityValueConf, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{}
	if sid > 0 {
		query["sid"] = sid
	}

	client := session.DB(ftsZhuiYaRecommendDB).C(opPopularityValueConf)
	iter := client.Find(query).Iter()

	var conf OpPopularityValueConf
	for iter.Next(&conf) {
		confs = append(confs, conf)
	}
	yylog.Info("", zap.Any("query", query), zap.Any("confs", confs), zap.Error(err))
	return
}

// SetOpPopularityValueConf 设置运营人气配置
func SetOpPopularityValueConf(conf OpPopularityValueConf) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(opPopularityValueConf)
	err = client.Update(bson.M{"_id": conf.ID},
		bson.M{"$set": bson.M{"sid": conf.Sid,
			"ssid":      conf.Ssid,
			"startTime": conf.StartTime,
			"endTime":   conf.EndTime,
			"perSecond": conf.PerSecond,
			"value":     conf.Value,
		}})
	if err == mgo.ErrNotFound {
		err = nil

		err = client.Insert(conf)
		if mgo.IsDup(err) {
			err = nil
		}
	}

	yylog.Info("", zap.Any("conf", conf), zap.Error(err))
	return
}

// GetRecommendBlackList 获取推荐黑名单列表
func GetRecommendBlackList(t int) (records []RecommendBlackList, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{}
	if t > 0 {
		query["type"] = t
	}

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendBlackList)
	iter := client.Find(query).Iter()
	var record RecommendBlackList
	for iter.Next(&record) {
		records = append(records, record)
	}
	yylog.Info("get recommend black list", zap.Any("query", query), zap.Int("len(records)", len(records)), zap.Error(err))
	return
}

// GetRecommendBlackListMap 获取推荐黑名单列表
func GetRecommendBlackListMap() (sidMap map[int64]bool, channelMap map[string]bool, uidMap map[int64]bool, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendBlackList)
	iter := client.Find(bson.M{}).Iter()
	var record RecommendBlackList
	for iter.Next(&record) {
		switch {
		case record.IsSidBlackListType():
			if sidMap == nil {
				sidMap = make(map[int64]bool)
			}
			sidMap[record.Sid] = true
		case record.IsChannelBlackList():
			if channelMap == nil {
				channelMap = make(map[string]bool)
			}
			c := fmt.Sprintf("%d:%d", record.Sid, record.Ssid)
			channelMap[c] = true
		case record.IsUIDBlackListType():
			if uidMap == nil {
				uidMap = make(map[int64]bool)
			}
			uidMap[record.UID] = true
		}
	}
	yylog.Info("get recommend black list map", zap.Int("len(sidMap)", len(sidMap)), zap.Int("len(channelMap)", len(channelMap)),
		zap.Int("len(uidMap)", len(uidMap)), zap.Error(err))
	return
}

// AddRecommendBlackList 添加黑名单
func AddRecommendBlackList(p *RecommendBlackList) (isDup bool, err error) {
	if p == nil {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendBlackList)
	err = client.Insert(p)
	if mgo.IsDup(err) {
		isDup = true
		err = nil
	}
	yylog.Info("add recommend black list", zap.Any("p", p), zap.Bool("isDup", isDup), zap.Error(err))
	return
}

// RemoveRecommendBlackList 删除黑名单
func RemoveRecommendBlackList(ids []string) (err error) {
	if len(ids) <= 0 {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendBlackList)

	_, err = client.RemoveAll(bson.M{"_id": bson.M{"$in": ids}})
	if err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("remove recommend black list", zap.Strings("ids", ids), zap.Error(err))
	return
}

// BatchUpdateRecommendBlackList 批量更新黑名单
func BatchUpdateRecommendBlackList(items []RecommendBlackList) (err error) {
	if len(items) <= 0 {
		return
	}
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	const n = 200
	client := session.DB(ftsZhuiYaRecommendDB).C(recommendBlackList)
	bulk := client.Bulk()
	for len(items) > n {
		for _, item := range items[:n] {
			bulk.Upsert(bson.M{"_id": item.ID}, bson.M{"$set": item})
			yylog.Info("", zap.Any("item", item))
		}
		_, err = bulk.Run()
		if err != nil {
			return
		}
		items = items[n:]
	}
	if len(items) > 0 {
		for _, item := range items {
			bulk.Upsert(bson.M{"_id": item.ID}, bson.M{"$set": item})
			yylog.Info("", zap.Any("item", item))
		}
		_, err = bulk.Run()
		if err != nil {
			return
		}
	}
	yylog.Info("batch update recommend black list", zap.Any("len(items)", len(items)), zap.Error(err))
	return
}

// GetRecommendSinkZoneConfig 获取沉底推荐列表
func GetRecommendSinkZoneConfig(sinkMode, terminal, category int, zoneID, uid, sid, ssid int64) (
	sinkList []RecommendSinkZoneConfig, configMap map[string]RecommendSinkZoneConfig, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{"sinkMode": sinkMode}
	if terminal > 0 {
		query["terminal"] = terminal
	}
	if category > 0 {
		query["category"] = category
	}
	if zoneID > 0 {
		query["zoneId"] = zoneID
	}

	if uid > 0 && IsRecommendCompere(sinkMode) {
		query["uid"] = uid
	}

	if sid > 0 {
		query["sid"] = sid
	}

	if ssid > 0 {
		query["ssid"] = ssid
	}

	var item RecommendSinkZoneConfig
	configMap = make(map[string]RecommendSinkZoneConfig)
	client := session.DB(ftsZhuiYaRecommendDB).C(recommendSinkConfig)
	iter := client.Find(query).Iter()
	for iter.Next(&item) {
		item.SinkConfigID = item.ID
		sinkList = append(sinkList, item)
		configMap[item.ID] = item
	}
	err = iter.Close()
	yylog.Info("", zap.Any("query", query), zap.Int("len(sinkList)", len(sinkList)), zap.Error(err))
	return
}

// AddRecommendSinkZoneConfig 插入
func AddRecommendSinkZoneConfig(record *RecommendSinkZoneConfig) (isDup int, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendSinkConfig)
	if err = client.Insert(record); err != nil {
		if mgo.IsDup(err) {
			isDup = 1
			err = nil
		}
	}
	yylog.Info("", zap.Any("record", record), zap.Error(err))
	return
}

// BulkAddRecommendSinkZoneConfig 批量插入
func BulkAddRecommendSinkZoneConfig(records []RecommendSinkZoneConfig) (bulkResult *mgo.BulkResult, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendSinkConfig)

	bulk := client.Bulk()
	for _, item := range records {
		bulk.Insert(item)
	}
	bulkResult, err = bulk.Run()
	yylog.Info("", zap.Int("len(records)", len(records)), zap.Any("bulkResult", bulkResult), zap.Error(err),
		zap.Any("addList", records))
	return
}

// RemoveRecommendSinkZoneConfig 删除推荐管理配置
func RemoveRecommendSinkZoneConfig(id string) (err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	client := session.DB(ftsZhuiYaRecommendDB).C(recommendSinkConfig)
	if err = client.RemoveId(id); err == mgo.ErrNotFound {
		err = nil
	}
	yylog.Info("", zap.String("ids", id), zap.Error(err))
	return
}

// BatchGetFormulaParameter 获取公式参数
func BatchGetFormulaParameter(zoneID []int64) (pramsMap map[int]FormulaParameter, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	var params []FormulaParameter
	client := session.DB(ftsZhuiYaRecommendDB).C(formulaParameter)
	err = client.Find(bson.M{"zoneid": bson.M{"$in": zoneID}}).All(&params)
	pramsMap = make(map[int]FormulaParameter, len(params))
	for i := 0; i < len(params); i++ {
		pramsMap[params[i].ZoneID] = params[i]
	}

	yylog.Info("", zap.Int64s("zoneID", zoneID), zap.Any("pramsMap", pramsMap), zap.Error(err))
	return
}

// GetRecommendSinkZoneDetail 查询当前推荐数据
func GetRecommendSinkZoneDetail(sinkMode, terminal, category int,
	zoneID, uid, sid, ssid, timestamp int64) (
	records []RecommendSinkZoneDetail, hitsMap map[string]struct{}, err error) {
	defer alert.MonitorAlert(&err)
	session := mgoSession.Clone()
	defer session.Close()

	query := bson.M{}
	if sinkMode > 0 {
		if IsRecommendCompere(sinkMode) { // 按主持查询时也查未分类的数据
			query = bson.M{"sinkMode": bson.M{"$in": []int{0, sinkMode}}}
		} else {
			query = bson.M{"sinkMode": sinkMode}
		}
	}

	if terminal > 0 {
		query["terminal"] = terminal
	}
	if category > 0 {
		query["category"] = category
	}
	if zoneID > 0 {
		query["zoneId"] = zoneID
	}

	if uid > 0 {
		query["uid"] = uid
	}

	if sid > 0 {
		query["sid"] = sid
	}

	if ssid > 0 {
		query["ssid"] = ssid
	}
	query["timestamp"] = bson.M{"$gte": timestamp}

	hitsMap = make(map[string]struct{})
	var record RecommendSinkZoneDetail
	client := session.DB(ftsZhuiYaRecommendDB).C(recommendSinkDetail)
	iter := client.Find(query).Iter()
	for iter.Next(&record) {
		records = append(records, record)
		hitsMap[record.SinkConfigID] = struct{}{}
	}
	err = iter.Close()
	yylog.Info("", zap.Int64("record timestamp", timestamp), zap.Int("len(records)", len(records)), zap.Error(err))
	return
}
