package v2

import (
	"context"
	"fmt"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
	"strconv"
	"time"

	"git.yy.com/golang/gfy/v2/yy/yylog"
	"go.uber.org/zap"

	"git.yy.com/server/jiaoyou/go_projects/api/common/alert"
	"git.yy.com/server/jiaoyou/go_projects/api/common/sentinelClient"
	"git.yy.com/server/jiaoyou/go_projects/diy/redigo/redis"
)

// SentinelClient redis sentinel client
var SentinelClient sentinelClient.SentinelClient

const (
	zhuiYaRecommendTimeLockPrefix = "zy:v2:time:lock"

	// banner
	zhuiYaRecommendBanner       = "zy:v2:banner:cache"        // 推荐banner缓存
	zhuiYaRecommendBannerPrefix = "zy:v2:banner"              // 推荐banner缓存前0
	zhuiYaTopFuncData           = "zy:v2:top_func:cache"      // 追玩快速入口配置
	zhuiYaYomiTopFuncData       = "zy:v2:yomi:top_func:cache" // Yomi快速入口配置
	zhuiYaYaYaTopFuncData       = "zy:v2:yaya:top_func:cache" // yaya快速入口配置
	zhuiYaAppReviewData         = "zy:v2:app_review:cache"    // 追玩app送审

	zhuiYaRecommendCachePrefix       = "zy:v2:cache"       // 推荐信息的缓存
	zhuiYaRecommendOnlineInfo        = "zy:v2:online"      // 在线信息
	zhuiYaRecommendWarningTimePrefix = "zy:v2:rw:time"     // 推荐数量不足，最近的一次的告警时间
	zhuiYaRecommendCacheTTL          = 7200                // 缓存TTL
	zhuiYaRecommendUpdateTime        = "zy:v2:update:time" // 推荐缓存开始更新时间
	zhuiYaRecommendRoomTag           = "zy:v2:tag"         // 豆腐块右上角的标签

	zhuiYaRecommendBabyOnline     = "zy:v2:recommend:baby"         // 正在推荐的宝贝主播
	zhuiYaRecommendStream         = "zy:v2:recommend:stream"       // 开播流
	zhuiYaRecommendFeatureInfo    = "zy:v2:recommend:feature:info" // 正在推荐频道的标题和推荐图
	zhuiYaNotRecommendCachePrefix = "zy:v2:not:recommend:cache"    // 未推荐信息的缓存
	zhuiYaRecommmendGuestIfo      = "zy:v2:recommend:guest"        // 嘉宾信息
)

// TimeLock 分布式锁
func TimeLock(lockKey string, ttlSec int64) (ret bool, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	key := fmt.Sprintf("%s:%s", zhuiYaRecommendTimeLockPrefix, lockKey)
	r, err := redis.String(client.Do("SET", key, time.Now().Unix(), "EX", ttlSec, "NX"))
	if err == nil && r == "OK" {
		ret = true
	}

	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("time lock", zap.String("key", key), zap.Int64("ttl", ttlSec), zap.Bool("ret", ret), zap.Error(err))
	return
}

// GetRecommendBannerKey banner缓存key
func GetRecommendBannerKey(business, platform, recommendType int64) string {
	return fmt.Sprintf(
		"%s:%d:%d:%d",
		zhuiYaRecommendBannerPrefix,
		business,
		platform,
		recommendType,
	)
}

// GetRecommendBannerByCategoryKey banner缓存key(首页顶部banner)
func GetRecommendBannerByCategoryKey(business, platform, category int64) string {
	return fmt.Sprintf(
		"%s:%d:%d:%d",
		zhuiYaRecommendBannerPrefix,
		business,
		platform,
		category,
	)
}

// SetRecommendBanner 缓存banner
func SetRecommendBanner(bannerConfigMap map[string]string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	args := redis.Args{zhuiYaRecommendBanner}
	for k, v := range bannerConfigMap {
		args = args.Add(k, v)
	}

	err = client.Send("DEL", zhuiYaRecommendBanner)
	if len(bannerConfigMap) > 0 {
		client.Send("HMSET", args...)
	}
	err = client.Flush()
	if err != nil {
		yylog.Error("", zap.Any("bannerConfigMap", bannerConfigMap), zap.Error(err))
		return
	}

	yylog.Info("", zap.Any("bannerConfigMap", bannerConfigMap), zap.Error(err))
	return
}

// GetRecommendBanner 获取缓存banner
func GetRecommendBanner(ctx context.Context, key, keyAll, keyPCAndAPP string) (data []string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	data, err = redis.Strings(client.Do(
		"HMGET",
		redis.Args{}.Add(zhuiYaRecommendBanner).AddFlat([]string{key, keyAll, keyPCAndAPP})...,
	))
	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("", fts.TraceID(ctx), zap.String("key", key), zap.String("keyAll", keyAll), zap.Strings("data", data), zap.Error(err))
	return
}

// SetZWQuickEntryData 缓存快速入口配置数据 key-business:recommendType value-数据
func SetZWQuickEntryData(data string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	_, err = client.Do("SET", zhuiYaTopFuncData, data)
	if err != nil {
		yylog.Warn("", zap.String("updateData", data), zap.Error(err))
	}
	yylog.Info("", zap.String("updateData", data), zap.Error(err))
	return
}

// GetZWQuickEntryData 获取快速入口配置数据
func GetZWQuickEntryData() (data string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	data, err = redis.String(client.Do("GET", zhuiYaTopFuncData))
	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("", zap.String("data", data), zap.Error(err))
	return
}

// SetYomiQuickEntryData 缓存快速入口配置数据 key-business:recommendType value-数据
func SetYomiQuickEntryData(data string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	_, err = client.Do("SET", zhuiYaYomiTopFuncData, data)
	if err != nil {
		yylog.Warn("", zap.String("updateData", data), zap.Error(err))
	}
	yylog.Info("", zap.String("updateData", data), zap.Error(err))
	return
}

// GetYomiQuickEntryData 获取快速入口配置数据
func GetYomiQuickEntryData() (data string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	data, err = redis.String(client.Do("GET", zhuiYaYomiTopFuncData))
	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("", zap.String("data", data), zap.Error(err))
	return
}

// SetYaYaQuickEntryData 缓存快速入口配置数据 key-business:recommendType value-数据
func SetYaYaQuickEntryData(data string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	_, err = client.Do("SET", zhuiYaYaYaTopFuncData, data)
	if err != nil {
		yylog.Error("cannot set yaya quick entry", zap.String("data", data), zap.Error(err))
	}
	yylog.Info("set yaya quick entry", zap.String("data", data))
	return
}

// GetYaYaQuickEntryData 获取快速入口配置数据
func GetYaYaQuickEntryData() (data string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	data, err = redis.String(client.Do("GET", zhuiYaYaYaTopFuncData))
	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("get yaya quick entry", zap.String("data", data), zap.Error(err))
	return
}

// SetZWAppReviewData 缓存追玩app送审
func SetZWAppReviewData(data string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	_, err = client.Do("SET", zhuiYaAppReviewData, data)
	if err != nil {
		yylog.Warn("", zap.String("updateData", data), zap.Error(err))
	}
	yylog.Info("", zap.String("updateData", data), zap.Error(err))
	return
}

// GetZWAppReviewData 获取追玩app送审
func GetZWAppReviewData() (data string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	data, err = redis.String(client.Do("GET", zhuiYaAppReviewData))
	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("", zap.String("data", data), zap.Error(err))
	return
}

// 设置缓存
func setCache(key, value string, ttlSec int64) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	if ttlSec > 0 {
		_, err = client.Do("SETEX", key, ttlSec, value)
	} else {
		_, err = client.Do("SET", key, value)
	}

	yylog.Info("set cache", zap.String("key", key), zap.Int64("ttl", ttlSec), zap.Error(err))
	return
}

// 获取缓存
func getCache(key string) (s string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetSlaverClient()
	defer client.Close()

	if len(key) == 0 {
		return
	}

	s, err = redis.String(client.Do("GET", key))
	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("get cache", zap.String("key", key), zap.Error(err))
	return
}

// SetRoomPlayTag 设置豆腐块右上角的标签
func SetRoomPlayTag(value string) error {
	return setCache(zhuiYaRecommendRoomTag, value, 0)
}

// GetRoomPlayTag 获取豆腐块右上角的标签
func GetRoomPlayTag() (string, error) {
	return getCache(zhuiYaRecommendRoomTag)
}

// SetRecommendViewItemCache 设置推荐信息的缓存
func SetRecommendViewItemCache(id int64, platform int, value string) error {
	return setCache(fmt.Sprintf("%s:%d:%d", zhuiYaRecommendCachePrefix, id, platform), value, zhuiYaRecommendCacheTTL)
}

// SetNotRecommendItemCache 设置未推荐信息的缓存
func SetNotRecommendItemCache(id int64, platform int, value string) error {
	return setCache(fmt.Sprintf("%s:%d:%d", zhuiYaNotRecommendCachePrefix, id, platform), value, zhuiYaRecommendCacheTTL)
}

// GetNotRecommendViewCache 获取未推荐信息的缓存
func GetNotRecommendViewCache(id int64, platform int) (string, error) {
	return getCache(fmt.Sprintf("%s:%d:%d", zhuiYaNotRecommendCachePrefix, id, platform))
}

// GetRecommendViewItemCache 获取推荐信息的缓存
func GetRecommendViewItemCache(id int64, platform int) (string, error) {
	return getCache(fmt.Sprintf("%s:%d:%d", zhuiYaRecommendCachePrefix, id, platform))
}

// GetRecommendWarningTime 获取对应推荐位的数量不足时，最近的一次的告警时间
func GetRecommendWarningTime(id int64, platform int) (string, error) {
	return getCache(fmt.Sprintf("%s:%d:%d", zhuiYaRecommendWarningTimePrefix, id, platform))
}

// SetRecommendWarningTime 设置对应推荐位的数量不足时，最近的一次的告警时间
func SetRecommendWarningTime(id int64, platform int, value string, ttl int64) (err error) {
	return setCache(fmt.Sprintf("%s:%d:%d", zhuiYaRecommendWarningTimePrefix, id, platform), value, ttl)
}

// GetRecommendUpdateTime 拉取更新时间戳，单位秒
func GetRecommendUpdateTime() (timestamp int64, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	timestamp, err = redis.Int64(client.Do("GET", zhuiYaRecommendUpdateTime))
	if err == redis.ErrNil {
		err = nil
	}
	yylog.Info("get recommend update time", zap.Int64("timestamp", timestamp), zap.Error(err))
	return
}

// AddBabyOnlineInfo 宝贝正在推荐的主播信息 channel->uid
func AddBabyOnlineInfo(m map[string]int64) (err error) {
	if len(m) == 0 {
		return
	}
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	err = client.Send("DEL", zhuiYaRecommendBabyOnline)
	if len(m) > 0 {
		err = client.Send("HMSET", redis.Args{}.Add(zhuiYaRecommendBabyOnline).AddFlat(m)...)
	}
	err = client.Flush()
	yylog.Info("add baby online info", zap.Int("size", len(m)), zap.Error(err))
	return
}

// GetBabyOnlineInfo 宝贝正在推荐的主播信息 channel->uid
func GetBabyOnlineInfo() (m map[string]int64, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetSlaverClient()
	defer client.Close()

	m, err = redis.Int64Map(client.Do("HGETALL", zhuiYaRecommendBabyOnline))
	yylog.Info("get baby online info", zap.Int("size", len(m)), zap.Error(err))
	return
}

// SetLiveSpeed 开播混画流
func SetLiveSpeed(stream map[string]string) (err error) {
	return hmsetStringMap(zhuiYaRecommendStream, stream)
}

// SetGuestInfo 缓存嘉宾信息
func SetGuestInfo(guestInfo map[string]string) (err error) {
	return hmsetStringMap(zhuiYaRecommmendGuestIfo, guestInfo)
}

func hmsetStringMap(key string, m map[string]string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	err = client.Send("DEL", key)
	if len(m) > 0 {
		err = client.Send("HMSET", redis.Args{}.Add(key).AddFlat(m)...)
	}
	err = client.Flush()
	yylog.Info("hmset", zap.Int("size", len(m)), zap.Error(err))
	return
}

// BatchGetGuestInfo 嘉宾信息
func BatchGetGuestInfo(channelStrList []string) (m map[string]string, err error) {
	return hmgetStringMap(zhuiYaRecommmendGuestIfo, channelStrList)
}

// GetAllLiveSpeed 开播混画流
func GetAllLiveSpeed() (ret map[string]string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetSlaverClient()
	defer client.Close()

	ret, err = redis.StringMap(client.Do("HGETALL", zhuiYaRecommendStream))
	yylog.Info("get all live speed", zap.Int("streamSize", len(ret)), zap.Error(err))
	return
}

// BatchGetLiveSpeed 开播混画流
func BatchGetLiveSpeed(channelStrList []string) (m map[string]string, err error) {
	return hmgetStringMap(zhuiYaRecommendStream, channelStrList)
}

// BatchSetRecommendFeatureInfo 正在推荐的标题和封面
func BatchSetRecommendFeatureInfo(m map[string]string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	if len(m) > 0 {
		_, err = client.Do("HMSET", redis.Args{}.Add(zhuiYaRecommendFeatureInfo).AddFlat(m)...)
	}
	yylog.Info("set recommend feature info", zap.Error(err), zap.Int("size", len(m)))
	return
}

func hmgetStringMap(key string, fields []string) (m map[string]string, err error) {
	if len(fields) == 0 {
		return nil, nil
	}
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetSlaverClient()
	defer client.Close()
	str, err := redis.Strings(client.Do("HMGET", redis.Args{}.Add(key).AddFlat(fields)...))
	if len(fields) != len(str) {
		return
	}
	m = make(map[string]string)
	for i, s := range str {
		m[fields[i]] = s
	}
	yylog.Info("hmget", zap.Error(err), zap.String("key", key), zap.Any("fieldsSize", len(fields)),
		zap.Any("size", len(m)))
	return
}

// BatchGetRecommendFeatureInfo 获取正在推荐的标题和封面
func BatchGetRecommendFeatureInfo(channelStrList []string) (m map[string]string, err error) {
	return hmgetStringMap(zhuiYaRecommendFeatureInfo, channelStrList)
}

// GetAllRecommendFeatureInfo 获取正在推荐的标题和封面
func GetAllRecommendFeatureInfo() (featureInfoMap map[string]string, err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetSlaverClient()
	defer client.Close()

	cursor := 0
	for {
		r, err := redis.Values(client.Do("HSCAN", zhuiYaRecommendFeatureInfo, cursor))
		if err != nil {
			yylog.Error("cannot scan recommend feature info", zap.Error(err), zap.Int("cursor", cursor))
			return nil, err
		}
		if len(r) == 0 {
			break
		}
		if len(r) > 0 {
			m, err := redis.StringMap(r[1], nil)
			if err != nil {
				return nil, err
			}
			for c, feature := range m {
				if featureInfoMap == nil {
					featureInfoMap = make(map[string]string)
				}
				featureInfoMap[c] = feature
			}
		}
		cursorStr, err := redis.String(r[0], nil)
		if err != nil {
			return nil, err
		}
		if len(cursorStr) == 0 {
			break
		}
		cursor, err = strconv.Atoi(cursorStr)
		if err != nil {
			return nil, err
		}
		if cursor == 0 {
			break
		}
	}
	yylog.Info("get all recommend feature info", zap.Int("size", len(featureInfoMap)), zap.Error(err))
	return
}

// RemoveRecommendFeatureInfo 删除频道推荐的标题和封面
func RemoveRecommendFeatureInfo(channelStrList []string) (err error) {
	defer alert.MonitorAlert(&err)
	client := SentinelClient.GetMasterClient()
	defer client.Close()

	_, err = client.Do("HDEL", redis.Args{}.Add(zhuiYaRecommendFeatureInfo).AddFlat(channelStrList)...)
	yylog.Info("remove recommend feature info", zap.Int("channelSize", len(channelStrList)), zap.Error(err))
	return
}
