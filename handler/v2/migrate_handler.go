package v2

import (
	"context"
	"git.yy.com/golang/gfy/v2/yy/yylog"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/hdrecommenddao"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts/gin/middleware"
	hdrecommend "git.yy.com/server/jiaoyou/go_projects/api/yrpc/hdrecommend"
	"git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/config"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strconv"
	"strings"
	"time"
)

type migrator struct {
}

var migrate = &migrator{}

// 检查是否是迁移的tabId
func (m *migrator) isMigrate(tabID int64) bool {
	if tab, ok := getRecommendTabConfig(tabID); ok {
		return config.Business.IsMigratedTabID(tabID) || config.Business.IsMigratedTerminalCategory(tab.Terminal, tab.Category)
	}
	return false
}

// 获取推荐信息
func (m *migrator) getRecommendInfo(c *gin.Context, req *recommendInfoReq) {
	var ret recommendInfoResp
	defer c.JSON(http.StatusOK, &ret)
	ctx := c.Request.Context()
	uid := c.GetInt64(middleware.LoginUID)
	if uid <= 0 {
		uid = getCookieUID(c)
	}
	if uid <= 0 {
		uid = req.UID
	}

	var header = map[string]string{}
	for k, v := range c.Request.Header {
		header[strings.ToLower(k)] = strings.Join(v, ",")
	}
	var _, seq = fts.ExtractTraceID(ctx)
	if len(seq) == 0 {
		seq = strconv.FormatInt(time.Now().UnixMilli(), 10)
	}
	var rpcReq = &hdrecommend.GetRecommendInfoReq{
		Uid:      uid,
		TabId:    req.ID,
		Platform: int32(req.Platform),
		Next:     int32(req.Next),
		UserIp:   c.ClientIP(),
		Headers:  header,
		HostName: req.HostName,
		Seq:      seq,
		Version:  req.Version,
	}
	yylog.Info("migrateGetRecommendInfo", fts.TraceID(ctx), fts.UID(uid), zap.String("seq", seq), zap.Any("req", req))
	rsp, err := hdrecommenddao.Client().GetRecommendInfo(ctx, rpcReq)
	if err != nil {
		ret.Status, ret.Msg = fts.StatusBadRequest, "服务器繁忙，请稍候再试"
		yylog.Warn("migrateGetRecommendInfo rpcFailed",
			fts.TraceID(ctx), fts.UID(uid), zap.String("seq", seq), zap.Error(err))
		return
	}
	ret.Status, ret.Msg = int(rsp.Status), rsp.Msg
	if rsp.Status != 0 {
		yylog.Warn("migrateGetRecommendInfo bizFailed",
			fts.TraceID(ctx), fts.UID(uid), zap.String("seq", seq), zap.Int("status", ret.Status), zap.String("msg", ret.Msg))
		return
	}

	ret.Next = int(rsp.Next)
	ret.Items = m.convertRecommendInfoList(rsp.List)
	ret.Module = m.convertRecommendModuleList(rsp.Module)
	ret.Banner = m.convertBannerInfoList(rsp.Banner)
	ret.Version = 1

	yylog.Info("migrateGetRecommendInfo success",
		fts.TraceID(ctx), fts.UID(uid), zap.String("seq", seq),
		zap.Int("itemSize", len(rsp.List)), zap.Int("bannerSize", len(rsp.Banner)), zap.Int("moduleSize", len(rsp.Module)),
		zap.Int("rItemSize", len(ret.Items)), zap.Int("rBannerSize", len(ret.Banner)), zap.Int("rModuleSize", len(ret.Module)),
		zap.Reflect("banner", rsp.Banner),
		zap.Reflect("rBanner", ret.Banner),
		zap.Int("next", ret.Next))
	return
}

func (m *migrator) getUnRecommendList(ctx context.Context, tabId int64, platform int) (rList []*notRecommendItem, err error) {
	yylog.Info("migrateGetUnRecommendList",
		fts.TraceID(ctx), zap.Int64("tabId", tabId), zap.Int("platform", platform))

	r, err := hdrecommenddao.Client().GetUnRecommendInfo(ctx, &hdrecommend.GetUnRecommendInfoReq{
		TabId:    tabId,
		Platform: int32(platform),
	})
	if err != nil {
		return nil, err
	}

	if len(r.List) > 0 {
		for _, item := range r.List {
			rList = append(rList, &notRecommendItem{
				UID:    item.Uid,
				Sid:    item.Sid,
				Ssid:   item.Ssid,
				ZoneID: tabId,
				Reason: item.Reason,
			})
		}
	}
	return
}

// 转换推荐信息列表
func (m *migrator) convertRecommendInfoList(list []*hdrecommend.RecommendVo) []*viewItem {
	if list == nil {
		return nil
	}

	r := make([]*viewItem, 0, len(list))
	for _, item := range list {
		if item == nil {
			continue
		}

		// 转换嘉宾信息
		onlineInfo := make([]guestInfo, 0, len(item.OnlineInfo))
		for _, guest := range item.OnlineInfo {
			if guest == nil {
				continue
			}
			onlineInfo = append(onlineInfo, guestInfo{
				UID:       guest.Uid,
				Avatar:    guest.Avatar,
				Nick:      guest.Nick,
				Gender:    guest.Gender,
				Seat:      guest.Seat,
				Avatar100: guest.Avatar100,
				Avatar60:  guest.Avatar60,
				AvatarHD:  guest.AvatarHd,
			})
		}

		// 转换匹配信息
		var matchInfo match
		if item.Match != nil {
			matchInfo = match{
				UID:      item.Match.Uid,
				Sid:      item.Match.Sid,
				Ssid:     item.Match.Ssid,
				GuestUID: item.Match.GuestUid,
			}
		}

		// 构建 viewItem
		vi := &viewItem{
			UID:                  item.Uid,
			Sid:                  item.Sid,
			Ssid:                 item.Ssid,
			Asid:                 item.Asid,
			Psu:                  item.Psu,
			Title:                item.Title,
			Nick:                 item.Nick,
			Cover:                item.Cover,
			ZoneID:               item.ZoneId,
			ConfZoneID:           item.ConfZoneID,
			Label:                item.Label,
			Attributes:           item.Attributes,
			AttributesIcon:       item.AttributesIcon,
			TemplateID:           item.TemplateId,
			Weight:               item.Weight,
			Mode:                 int(item.Mode),
			Sex:                  int(item.Sex),
			Position:             item.Position,
			Business:             int(item.Business),
			Platform:             int(item.Platform),
			GameType:             int(item.GameType),
			LabelExpired:         item.LabelExpired,
			ShowAttributesStatus: item.ShowAttributesStatus,
			Icon:                 item.Icon,
			BigIcon:              item.BigIcon,
			MatchStatus:          item.MatchStatus,
			SubTitle:             item.SubTitle,
			ViewType:             viewType(item.ViewType),
			GameID:               int(item.GameId),
			GameName:             item.GameName,
			RoomTag:              item.RoomTag,
			Lock:                 item.Lock,
			Background:           item.Background,
			LiveInfo:             item.LiveInfo,
			GuestUID:             item.GuestUid,
			Match:                matchInfo,
			OnlineInfo:           onlineInfo,
			EnterType:            item.EnterType,
			RoomType:             int(item.RoomType),
			RoomClass:            int(item.RoomClass),
			Terminal:             m.convertTerminal(item.Terminal),
			MixStream:            item.MixStream,
			RecommendManageID:    item.RecommendManageId,
			RoomName:             item.RoomName,
			RoomCover:            item.RoomCover,
			AttributesStatus:     item.AttributesStatus,
			RcAttributesStatus:   item.RcAttributesStatus,
			SideAttributesStatus: item.SideAttributesStatus,
			Avatar:               item.Avatar,
			CardStyle:            int(item.CardStyle),
			RoomNo:               item.RoomNo,
			SubRoomClass:         int(item.SubRoomClass),
			BackgroundNew:        item.BackgroundNew,
			BdCover:              item.BdCover,
			BdTitle:              item.BdTitle,
			LayoutConfig:         item.LayoutConfig,
			RecommendBgImgUrl:    item.RecommendBgImgUrl,
			GameTypeIcon:         item.GameTypeIcon,
			SubGameTypeIcon:      item.SubGameTypeIcon,
			StatusText:           item.StatusText,
			OnlineCnt:            item.OnlineCnt,
			ExtendAttrList:       convertExtendAttrList(item.ExtendAttr),
		}
		r = append(r, vi)
	}
	return r
}

func convertExtendAttrList(attrList []*hdrecommend.ExtendAttrVo) (r []extendAttrVo) {
	if len(attrList) == 0 {
		return
	}
	for _, attr := range attrList {
		r = append(r, extendAttrVo{
			Icon:    attr.Icon,
			Title:   attr.Title,
			Content: attr.Content,
		})
	}
	return
}

// 转换推荐模块列表
func (m *migrator) convertRecommendModuleList(modules []*hdrecommend.RecommendModuleVo) []*recommendModule {
	if modules == nil {
		return nil
	}

	r := make([]*recommendModule, 0, len(modules))
	for _, module := range modules {
		if module == nil {
			continue
		}

		rm := &recommendModule{
			Title:    module.Title,
			Position: int(module.Position),
			Items:    m.convertRecommendInfoList(module.List),
		}
		r = append(r, rm)
	}
	return r
}

// 转换 Banner 信息列表
func (m *migrator) convertBannerInfoList(banners []*hdrecommend.BannerInfoVo) []bannerInfo {
	if banners == nil {
		return nil
	}

	r := make([]bannerInfo, 0, len(banners))
	for _, banner := range banners {
		if banner == nil {
			continue
		}

		// 转换 banner 资源列表
		resList := make([]bannerData, 0, len(banner.ResList))
		for _, res := range banner.ResList {
			if res == nil {
				continue
			}
			resList = append(resList, bannerData{
				Name:         res.Name,
				ImageURL:     res.ImageUrl,
				Sort:         int(res.Sort),
				JumpType:     int(res.JumpType),
				JumpPosition: res.JumpPosition,
				BusinessID:   res.BusinessId,
				UniqueID:     res.UniqueId,
			})
		}

		bi := bannerInfo{
			Name:            banner.Name,
			Type:            int(banner.Type),
			Count:           int(banner.Count),
			DisplayPosition: int(banner.DisplayPosition),
			ResList:         resList,
		}
		r = append(r, bi)
	}
	return r
}

// 辅助函数：转换终端列表
func (m *migrator) convertTerminal(terminals []int32) []int {
	if terminals == nil {
		return nil
	}
	r := make([]int, len(terminals))
	for i, t := range terminals {
		r[i] = int(t)
	}
	return r
}

func (m *migrator) getTopInfo(c *gin.Context) (migrated bool) {
	if config.Business.MigrateGetTopInfo {
		yylog.Debug("use migrate get top info")
		proxyURL, _ := url.Parse(config.Business.MigrateHost + "/zhuiya_recommend/v2/get_top_info")
		// 创建反向代理
		proxy := httputil.NewSingleHostReverseProxy(proxyURL)
		c.Request.URL.Host = proxyURL.Host
		c.Request.URL.Scheme = proxyURL.Scheme
		c.Request.RequestURI = ""
		// 使用反向代理处理请求
		proxy.ServeHTTP(c.Writer, c.Request)
		return true
	}
	return false
}
