package v2

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.yy.com/server/jiaoyou/go_projects/api/common/fts/gin/middleware"

	"git.yy.com/server/jiaoyou/go_projects/api/common/constinfo"

	"git.yy.com/golang/gfy/v2/yy/yylog"
	"go.uber.org/zap"

	"github.com/gin-gonic/gin"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/webdbdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/gamecenterdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/zhuiwandao"

	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
	"git.yy.com/server/jiaoyou/go_projects/api/common/util"
	mgodao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/mgodao/v2"
	redisdao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/redisdao/v2"
)

const (
	channelURL = "zhuiwan://channel/live/%d/%d/%d"
	pageURL    = "zhuiwan://browser/%s"
	normalURL  = "%s"

	// 每个banner最多5条
	maxBannerCount = 5
	maxTry         = 3
)

// 推荐类型
const (
	bannerTopPosition = -1
)

var (
	// category 交友转追玩
	jyHomePageToZW = map[int64]int64{
		categoryGame:       categoryGameZW,
		categoryRecreation: categoryRecreationZW,
		categoryChatRoom:   categoryChatRoomZW,
		categoryDiscovery:  categoryDiscoveryZW,
		categoryQuickGame:  categoryQuickGameZW,
	}
)

// 获取banner位置类型的数据
const (
	bannerPosTypeAll    = 0 // 获取全部
	bannerPosTypeTop    = 1 // 只获取顶部
	bannerPosTypeNotTop = 2 // 获取非顶部
)

const (
	jumpChannelHadTemplateIDFlag = true
	jumpChannelVersion           = "4.0.0"
)

// GetTopInfo 推荐顶部banner和快速入口
// @Summary 推荐顶部banner和快速入口
// @Description 推荐顶部banner和快速入口; https://jywiki.yy.com/docs/other_apis//798
// @Tags 推荐顶部
// @Accept json
// @Produce json
// @param business query int true "业务类型"
// @param category query int true "页面类型"
// @param x-fts-host-name header string true "终端;yomi/yokh/yaya"
// @param x-fts-platform header int true "平台;1-pc/2-pcweb/3-android/4-ios"
// @param x-fts-host-version header string true "版本;x.x.x"
// @param YYHeader-Market header string true "渠道包"
// @Success 200 {object} fts.RespResult{status=int,data=bannerAllRespond}
// @Router /v2/get_top_info [get]
func GetTopInfo(c *gin.Context) {
	rw, req := c.Writer, c.Request
	var (
		ret     resultZW
		respond bannerAllRespond
	)
	start := time.Now()
	defer util.HttpRespJson(rw, req, &ret)
	ctx := c.Request.Context()

	uid := c.GetInt64(middleware.LoginUID)

	platform, version, market, business, category, err := getTopInfoParam(req)
	if err != nil {
		yylog.Warn("request param err", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	// 特殊处理: yo和追玩共用一个娱乐页面
	quickEntryBusiness := business
	if category == categoryRecreation && business == constinfo.BusinessTypeYOMI {
		business = constinfo.BusinessTypeZW
	}

	// 快速入口配置
	zwCategory := category
	if value, ok := jyHomePageToZW[category]; ok {
		zwCategory = value
	}

	yylog.Info(
		"",
		fts.TraceID(ctx),
		fts.UID(uid),
		zap.Any("platform", platform),
		zap.String("version", version),
		zap.String("market", market),
		zap.Int64("business", business),
		zap.Int64("category", category),
		zap.Int64("zwCategory", zwCategory),
	)

	// 获取banner
	respond.Banner, err = GetBannerData(ctx, business, platform, category, 0, bannerPosTypeTop, version)
	if err != nil {
		yylog.Warn("", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
	}

	// 快速入口配置
	quickEntryKey := getQuickEntryKey(quickEntryBusiness, zwCategory)
	filterKey := getFilterKey(platform, zwCategory, market, version)
	// quickEntryList, err := GetQuickEntryList(traceID, quickEntryKey, filterKey)
	quickEntryList, err := quickEntryDealV2(ctx, quickEntryKey, filterKey)
	if err != nil {
		yylog.Warn("", fts.TraceID(ctx), zap.Error(err))
	}
	respond.QuickEntry = quickEntryList

	ret.Data = respond

	yylog.Info("", fts.TraceID(ctx), fts.UID(uid), zap.Int64("cost", time.Since(start).Milliseconds()), zap.Any("respond", respond))
}

// GetBannerInfo 推荐banner
func GetBannerInfo(c *gin.Context) {
	rw, req := c.Writer, c.Request
	var (
		ret resultZW
	)
	defer util.HttpRespJson(rw, req, &ret)

	ctx := c.Request.Context()

	platform, business, _, recommendType, err := getBannerParam(req)
	if err != nil {
		yylog.Warn("request param err", fts.TraceID(ctx), zap.Error(err))
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	yylog.Info("", fts.TraceID(ctx), zap.Int64("business", business), zap.Any("platform", platform), zap.Int64("recommendType", recommendType))

	// 获取banner
	banner, err := GetBannerData(ctx, business, platform, 0, recommendType, bannerPosTypeNotTop, "")
	if err != nil {
		yylog.Error("", fts.TraceID(ctx), zap.Int64("business", business), zap.Any("platform", platform),
			zap.Int64("recommendType", recommendType), zap.Error(err))
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	yylog.Info("", fts.TraceID(ctx), zap.Int64("business", business), zap.Any("platform", platform),
		zap.Int64("recommendType", recommendType), zap.Any("banner", banner))

	ret.Data = banner
}

// getTopInfoParam 获取请求参数
func getTopInfoParam(req *http.Request) (
	platform Platform, version, market string, business, category int64, err error) {
	market = fts.NewRequest(req).GetYYHeaderMarket()

	mark := fts.NewHTTPHeaderMark(req.Header)
	if len(mark.HostVersion) == 0 {
		mark.HostVersion = req.URL.Query().Get("version")
	}
	if len(mark.HostName) == 0 {
		mark.HostName = req.URL.Query().Get("hostName")
	}

	platform = platformHeaderToPlatform(mark)
	if platform == 0 {
		inputPlatform, _ := getReqParamToInt64(req, "platform")
		platform = Platform(inputPlatform)
	}

	v := mark.GetHostVersion()
	version = v.Format()

	business, err = getReqParamToInt64(req, "business")
	if err != nil {
		return
	}

	business = hostNameToBusiness(mark)

	category, err = getReqParamToInt64(req, "category")
	if err != nil {
		return
	}

	return
}

// getBannerParam 获取请求参数
func getBannerParam(req *http.Request) (
	platform Platform,
	business, category, recommendType int64,
	err error,
) {
	mark := fts.NewHTTPHeaderMark(req.Header)
	if len(mark.HostVersion) == 0 {
		mark.HostVersion = req.URL.Query().Get("version")
	}
	if len(mark.HostName) == 0 {
		mark.HostName = req.URL.Query().Get("hostName")
	}

	platform = platformHeaderToPlatform(mark)
	if platform == 0 {
		inputPlatform, _ := getReqParamToInt64(req, "platform")
		platform = Platform(inputPlatform)
	}

	// 语音房web(yo语音web)化特殊处理
	if mark.IsPlatformAndroid() && mark.HostName == "yomiweb" {
		platform = platformPCWeb
	}

	business, err = getReqParamToInt64(req, "business")
	if err != nil {
		return
	}

	business = hostNameToBusiness(mark)

	recommendType, err = getReqParamToInt64(req, "recommendType")
	if err != nil {
		return
	}

	return
}

// buildQuickEntryCache 构建快速入口配置
func buildQuickEntryCache() (string, error) {
	res, err := zhuiwandao.GetHomepageFunctions(0)
	if err != nil {
		yylog.Error("GetHomepageFunctions", zap.Error(err))
		return "", err
	}

	data, err := json.Marshal(res.Functions)
	if err != nil {
		return "", err
	}

	yylog.Debug("", zap.String("data", string(data)))

	return string(data), nil
}

// buildYomiQuickEntryCache 构建快速入口配置
func buildYomiQuickEntryCache() (string, error) {
	res, err := zhuiwandao.GetYomiHomepageFunctions(0)
	if err != nil {
		yylog.Error("GetHomepageFunctions", zap.Error(err))
		return "", err
	}

	data, err := json.Marshal(res.Functions)
	if err != nil {
		return "", err
	}

	yylog.Debug("", zap.String("data", string(data)))

	return string(data), nil
}

// buildYaYaQuickEntryCache 构建快速入口配置
func buildYaYaQuickEntryCache() (string, error) {
	res, err := zhuiwandao.GetYayaHomepageFunctions(0)
	if err != nil {
		yylog.Error("GetYayaHomepageFunctions", zap.Error(err))
		return "", err
	}

	data, err := json.Marshal(res.Functions)
	if err != nil {
		return "", err
	}

	yylog.Debug("build yaya quick entry cache", zap.String("data", string(data)))

	return string(data), nil
}

// buildBannerCache 构造banner缓存
func buildBannerCache() (map[string]string, error) {
	list, err := mgodao.GetBannerConfigValidList()
	if err != nil {
		yylog.Error("get banner list", zap.Error(err))
		return nil, err
	}
	yylog.Debug("", zap.Int("len(list)", len(list)))

	onlineChannelMap := getOnlineChannel()
	yylog.Debug("", zap.Any("onlineChannelMap", onlineChannelMap))

	now := time.Now().Unix()
	bannerConfigMap := make(map[string]string)
	bannerInfoCache := make(map[string][]bannerInfo)
	bannerInfoByCategoryCache := make(map[string][]bannerInfo)
	for _, info := range list {
		if isTimeExpire(now, info.ValidStartTime, info.ValidEndTime) {
			yylog.Info("", zap.Int("status", info.Status), zap.Int64("now", now), zap.Int64("start", info.ValidStartTime),
				zap.Int64("start", info.ValidEndTime))
			continue
		}

		banner := bannerInfo{
			Name:            info.Name,
			Type:            info.Type,
			Count:           info.Count,
			DisplayPosition: info.DisplayPosition,
		}

		for _, data := range info.Data {
			if data.Status == mgodao.StatusTypeInvalid {
				continue
			}

			if isTimeExpire(now, data.ValidStartTime, data.ValidEndTime) {
				yylog.Info("", zap.Int("status", info.Status), zap.Int64("now", now), zap.Int64("start", info.ValidStartTime),
					zap.Int64("start", info.ValidEndTime))
				continue
			}

			var (
				sid, ssid int64
				urlStr    string
			)
			switch data.JumpType {
			case mgodao.JumpTypeChannel:
				urlStr, sid, ssid = jumpChannel(data, onlineChannelMap)
			case mgodao.JumpTypePage:
				urlStr = jumpPage(data)
			case mgodao.JumpTypeNormal:
				urlStr = jumpNormal(data)
			default:
				yylog.Warn("can not support jump type", zap.Int("type", data.JumpType))
				continue
			}

			if len(urlStr) == 0 {
				yylog.Warn("url empty", zap.Any("data", data))
				continue
			}

			yylog.Debug("", zap.Any("data", data))
			banner.ResList = append(banner.ResList, bannerData{
				Name:         data.Name,
				ImageURL:     data.ImageURL,
				Sort:         data.Sort,
				JumpType:     data.JumpType,
				JumpPosition: urlStr,
				BusinessID:   jumpChannelBusinessID(sid, ssid, onlineChannelMap),
				UniqueID:     data.UniqueID,
			})
		}

		if len(banner.ResList) == 0 {
			yylog.Warn("banner resList empty", zap.Any("banner", banner))
			continue
		}

		// 排序显示且根据排序选出最多5条
		yylog.Debug("", zap.Any("banner", banner.ResList))
		banner = selectBannerResList(banner)
		yylog.Debug("", zap.Any("banner", banner.ResList))

		key := redisdao.GetRecommendBannerKey(
			int64(info.Business),
			int64(info.Platform),
			int64(info.RecommendType),
		)
		bannerInfoCache[key] = append(bannerInfoCache[key], banner)

		key2 := redisdao.GetRecommendBannerByCategoryKey(
			int64(info.Business),
			int64(info.Platform),
			int64(info.Category),
		)
		bannerInfoByCategoryCache[key2] = append(bannerInfoByCategoryCache[key2], banner)
	}

	for key, bannerConfig := range bannerInfoCache {
		yylog.Debug("bannerInfoCache1", zap.String("key", key), zap.Any("bannerConfig", bannerConfig))
		bannerConfigStr, err := json.Marshal(bannerConfig)
		if err != nil {
			yylog.Warn("", zap.String("key", key), zap.Any("bannerConfig", bannerConfig), zap.Error(err))
			continue
		}
		bannerConfigMap[key] = string(bannerConfigStr)
		yylog.Debug("bannerInfoCache2", zap.String("key", key), zap.String("bannerConfigStr", string(bannerConfigStr)))
	}

	for key, bannerConfig := range bannerInfoByCategoryCache {
		yylog.Debug("bannerInfoByCategoryCache1", zap.String("key", key), zap.Any("bannerConfig", bannerConfig))
		bannerConfigStr, err := json.Marshal(bannerConfig)
		if err != nil {
			yylog.Warn("", zap.String("key", key), zap.Any("bannerConfig", bannerConfig), zap.Error(err))
			continue
		}
		bannerConfigMap[key] = string(bannerConfigStr)
		yylog.Debug("bannerInfoByCategoryCache2", zap.String("key", key), zap.String("bannerConfigStr", string(bannerConfigStr)))
	}

	return bannerConfigMap, nil
}

// getOnlineChannel 获取在线频道信息
func getOnlineChannel() map[sidInfo]*onlineChannelInfo {
	var onlineChannelMap = make(map[sidInfo]*onlineChannelInfo)
	for try := 0; try < maxTry; try++ {
		jyOnlineChannelInfo, err := getJYOnlineChannel()
		if err != nil {
			yylog.Error("GetAllOnlineChannel", zap.Error(err))
			if try < maxTry-1 {
				time.Sleep(time.Duration(try+1) * time.Millisecond * 500)
			}
			continue
		}

		for _, info := range jyOnlineChannelInfo {
			onlineChannelMap[sidInfo{Sid: info.Sid, SSid: info.Ssid}] = info
		}

		yylog.Info("", zap.Int("jyOnlineChannelInfo len", len(jyOnlineChannelInfo)))
		break
	}

	for try := 0; try < maxTry; try++ {
		pgOnlineChannelInfo, err := getZWOnlineChannelInfo()
		if err != nil {
			yylog.Error("getPWAndGameOnlineChannelInfo", zap.Error(err))
			if try < maxTry-1 {
				time.Sleep(time.Duration(try+1) * time.Millisecond * 500)
			}
			continue
		}

		for _, list := range pgOnlineChannelInfo {
			for _, info := range list {
				onlineChannelMap[sidInfo{Sid: info.Sid, SSid: info.Ssid}] = info
			}
		}
		yylog.Info("", zap.Int("pgOnlineChannelInfo len", len(pgOnlineChannelInfo)))
		break
	}

	yylog.Debug("", zap.Any("onlineChannelMap", onlineChannelMap))

	return onlineChannelMap
}

// buildAppReviewCache 构建追玩App送审信息
func buildAppReviewCache() (string, error) {
	res, err := zhuiwandao.GetAppReviewMsgs()
	if err != nil {
		yylog.Error("GetAppReviewMsgs", zap.Error(err))
		return "", err
	}

	data, err := json.Marshal(res.ReviewMsgs)
	if err != nil {
		yylog.Error("json marshal", zap.Error(err))
		return "", err
	}

	yylog.Info("", zap.String("data", string(data)))

	return string(data), nil
}

// getJYOnlineChannel 获取交友开播频道
func getJYOnlineChannel() ([]*onlineChannelInfo, error) {
	onlineCompere, err := gamecenterdao.GetAllOnlineChannel()
	if err != nil || onlineCompere == nil || onlineCompere.Ret != 0 {
		return nil, fmt.Errorf("GetAllOnlineChannel err: %+v, r: %+v", err, onlineCompere)
	}

	var channels []*Channel
	onlineUIDs := make(map[Channel]int64)
	for _, r := range onlineCompere.Info {
		yylog.Debug("", zap.Any("info", r))
		if r == nil {
			continue
		}
		if r.Sid == 0 || r.Ssid == 0 || r.UID == 0 {
			continue
		}
		channel := Channel{
			Sid:  r.Sid,
			Ssid: r.Ssid,
		}
		channels = append(channels, &channel)
		onlineUIDs[channel] = r.UID
	}

	gameInfo, err := gamecenterdao.BatchGetGameInfoMap(channels)
	if err != nil {
		return nil, fmt.Errorf("BatchGetGameInfoMap err: %+v, r: %+v", err, gameInfo)
	}

	var onlineChannelInfoList []*onlineChannelInfo
	for channel, uid := range onlineUIDs {
		var gameType int64
		info, ok := gameInfo[channel]
		if !ok {
			yylog.Warn("", zap.Any("channel", channel), fts.UID(uid))
		}
		if info != nil {
			gameType = info.GetGameType()
		}
		yylog.Debug("", zap.Any("channel", channel), fts.UID(uid), zap.Int64("gameType", gameType))

		onlineChannelInfoList = append(onlineChannelInfoList, &onlineChannelInfo{
			Sid:      channel.Sid,
			Ssid:     channel.Ssid,
			UID:      uid,
			GameType: int(gameType),
		})
	}
	return onlineChannelInfoList, nil
}

// isTimeExpire 判断是否在有效期, true-无效 false-有效
func isTimeExpire(now, start, end int64) bool {
	return now < start || now > end
}

// jumpChannel 跳页面
func jumpChannel(data mgodao.BannerConfigData, onlineChannelMap map[sidInfo]*onlineChannelInfo) (urlStr string, sid int64, ssid int64) {
	if len(data.JumpPosition) == 0 {
		return
	}

	var (
		channelInfoList []channelInfo
	)
	// type,sid1,ssid1
	// type,sid2,ssid2
	// type,sid3,ssid3
	ssidSsidLists := strings.Split(data.JumpPosition, "\n")
	for _, list := range ssidSsidLists {
		channelInfoTmp := strings.Split(list, ",")
		yylog.Debug("", zap.Any("channelInfoTmp", channelInfoTmp))
		if len(channelInfoTmp) != 3 {
			yylog.Warn("list not 3", zap.Any("list", list))
			continue
		}

		ty, _ := strconv.ParseInt(channelInfoTmp[0], 10, 64)
		sid, _ = strconv.ParseInt(channelInfoTmp[1], 10, 64)
		ssid, _ = strconv.ParseInt(channelInfoTmp[2], 10, 64)

		yylog.Debug("", zap.Int64("ty", ty), fts.SSID(sid), fts.SID(ssid))

		// 过滤未开播的频道
		online := filterNotOnlineChannel(sid, ssid, onlineChannelMap)
		if !online {
			yylog.Info("not online", zap.Int64("ty", ty), fts.SSID(sid), fts.SID(ssid))
			continue
		}

		channelInfoList = append(channelInfoList, channelInfo{
			Type: ty,
			Sid:  sid,
			Ssid: ssid,
		})
	}

	if len(channelInfoList) == 0 {
		yylog.Warn("channelInfoList empty")
		return
	}

	index := rand.Intn(len(channelInfoList))
	yylog.Debug("", zap.Int("index", index), zap.Any("channelInfoList", channelInfoList))

	sid, ssid = channelInfoList[index].Sid, channelInfoList[index].Ssid
	urlStr = fmt.Sprintf(channelURL, channelInfoList[index].Type, sid, ssid)

	// 跳频道带模板id
	if jumpChannelHadTemplateIDFlag {
		channelStr := fmt.Sprintf("%d:%d", sid, ssid)
		res, err := webdbdao.BatchGetChannelTemplateID([]string{channelStr})
		if err != nil {
			yylog.Error("BatchGetChannelTemplateID", zap.Error(err))
			return
		}
		if templateID, ok := res[channelStr]; ok {
			urlStr = fmt.Sprintf("%s?tpl=%d", urlStr, templateID)
		}
	}

	return
}

// jumpPage 跳页面
func jumpPage(data mgodao.BannerConfigData) (urlStr string) {
	if len(data.JumpPosition) == 0 {
		return
	}
	urlStr = fmt.Sprintf(pageURL, data.JumpPosition)
	return
}

// jumpNormal 跳通用, 原样返回配置内容
func jumpNormal(data mgodao.BannerConfigData) (urlStr string) {
	if len(data.JumpPosition) == 0 {
		return
	}
	urlStr = fmt.Sprintf(normalURL, data.JumpPosition)
	return
}

// jumpChannelBusinessID 获取跳频道的玩法类型
func jumpChannelBusinessID(sid, ssid int64, onlineChannelMap map[sidInfo]*onlineChannelInfo) (businessID int64) {
	sidInfoTmp := sidInfo{Sid: sid, SSid: ssid}
	if onlineChannelMap[sidInfoTmp] != nil {
		businessID = int64(onlineChannelMap[sidInfoTmp].GameType)
	}
	return
}

// selectBannerResList 整理选择banner的资源
func selectBannerResList(banner bannerInfo) bannerInfo {
	rand.Shuffle(len(banner.ResList), func(i, j int) {
		banner.ResList[i], banner.ResList[j] = banner.ResList[j], banner.ResList[i]
	})
	sort.Slice(banner.ResList, func(i, j int) bool {
		if banner.ResList[i].Sort > banner.ResList[j].Sort {
			return true
		}
		return false
	})

	minCount := maxBannerCount
	if banner.Count < maxBannerCount {
		minCount = banner.Count
	}
	if len(banner.ResList) > minCount {
		banner.ResList = banner.ResList[:minCount]
	}

	return banner
}

// filterNotOnlineChannel 过滤掉不在线的频道 true-在线 false-不在线
func filterNotOnlineChannel(sid, ssid int64, onlineChannelMap map[sidInfo]*onlineChannelInfo) bool {
	sidInfoTmp := sidInfo{Sid: sid, SSid: ssid}
	if onlineChannelMap[sidInfoTmp] == nil {
		yylog.Info("sid not startBroadcast", fts.SSID(sid), fts.SID(ssid))
		return false
	}
	return true
}

// GetBannerData 获取banner数据
// business 业务类型
// business 平台
// category 页面类型 0-非顶部banner 1/2-顶部banner
// recommendType 页面tab id
// bannerPosType TODO 获取banner位置类型的数据(0-获取全部/1-只获取顶部/2-获取非顶部) 非顶部与页面tab有关
func GetBannerData(
	ctx context.Context,
	business int64,
	platform Platform,
	category int64,
	recommendType int64,
	bannerPosType int,
	version string,
) (banner []bannerInfo, err error) {
	banner = make([]bannerInfo, 0)
	var key, keyAll, keyPCAndAPP string
	switch bannerPosType {
	case bannerPosTypeTop:
		// 只获取顶部
		key = redisdao.GetRecommendBannerByCategoryKey(business, int64(platform), category)
		keyAll = redisdao.GetRecommendBannerByCategoryKey(
			business,
			int64(platformAndroidAndIOS),
			category,
		)
		keyPCAndAPP = redisdao.GetRecommendBannerByCategoryKey(business, int64(platformPCAndAPP), category)
	case bannerPosTypeAll:
	case bannerPosTypeNotTop:
		key = redisdao.GetRecommendBannerKey(business, int64(platform), recommendType)
		keyAll = redisdao.GetRecommendBannerKey(
			business,
			int64(platformAndroidAndIOS),
			recommendType,
		)
		keyPCAndAPP = redisdao.GetRecommendBannerKey(business, int64(platformPCAndAPP), recommendType)
	}

	bannerConfigList, err := redisdao.GetRecommendBanner(ctx, key, keyAll, keyPCAndAPP)
	if err != nil {
		yylog.Error("GetRecommendBanner", fts.TraceID(ctx), zap.String("key", key), zap.Error(err))
		return
	}

	// banner 只获取顶部
	for _, bannerConfigStr := range bannerConfigList {
		if len(bannerConfigStr) == 0 {
			continue
		}

		var bannerConfigsTmp []bannerInfo
		if err = json.Unmarshal([]byte(bannerConfigStr), &bannerConfigsTmp); err != nil {
			yylog.Error("", fts.TraceID(ctx), zap.String("bannerConfigStr", bannerConfigStr), zap.Error(err))
			continue
		}

		for _, info := range bannerConfigsTmp {
			// 追玩android都带, ios4.0后带 zhuiwan://live/{type}/{sid}/{ssid}?tpl=templateId
			if jumpChannelHadTemplateIDFlag {
				info.ResList = jumpChannelHadTemplateID(platform, version, info.ResList)
			}

			switch bannerPosType {
			case bannerPosTypeAll:
				banner = append(banner, info)
			case bannerPosTypeTop:
				if info.DisplayPosition == bannerTopPosition {
					banner = append(banner, info)
				}
			case bannerPosTypeNotTop:
				if info.DisplayPosition != bannerTopPosition {
					banner = append(banner, info)
				}
			}
		}
	}

	for i := 0; i < len(banner); i++ {
		for j := 0; j < len(banner[i].ResList); j++ {
			banner[i].ResList[j].ImageURL = strings.Replace(banner[i].ResList[j].ImageURL, "http:", "https:", 1)
		}
	}

	yylog.Info("", fts.TraceID(ctx), zap.Any("banner", banner))
	return
}

// jumpChannelHadTemplateID 跳频道默认带模板id, 请求时判断版本号是否满足, ios小于4.0.0不带模板id
func jumpChannelHadTemplateID(platform Platform, version string, resList []bannerData) []bannerData {
	if len(version) == 0 {
		// 无版本不校验
		return resList
	}

	for k, res := range resList {
		if res.JumpType == mgodao.JumpTypeChannel &&
			platform == Platform(platformIOS) && util.CompareVersion(version, jumpChannelVersion) == -1 {
			// 去掉模板id
			index := strings.LastIndex(resList[k].JumpPosition, "?")
			if index > 0 {
				resList[k].JumpPosition = resList[k].JumpPosition[:index]
			}
		}
	}
	return resList
}
