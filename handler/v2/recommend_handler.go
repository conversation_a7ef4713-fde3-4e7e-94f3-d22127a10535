package v2

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/hdgamedao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/infoflowagentdao"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_game_center"
	hdgame "git.yy.com/server/jiaoyou/go_projects/api/yrpc/hdgame"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/skillcardroomafkdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/userinfodao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/gameroledao"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_game_role"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/recommenddatabasedao"

	"git.yy.com/golang/gfy/v2/gfy"

	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_zhuiya_recommend_v2"

	"git.yy.com/golang/gfy/v2/yy/yylog"
	"go.uber.org/zap"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-thrift/fts_supplement"

	"git.yy.com/server/jiaoyou/go_projects/api/common/alert"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/recommendfilterdao"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_recommend_filter"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_live_afk"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/liveafkdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/supplementdao"
	proto_stream_manager "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/liveinfodao/proto"

	"github.com/golang/protobuf/proto"

	"git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/liveinfodao"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/zhuiwan_channel"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/zhuiwandao"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_video_dating"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/videodatingdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/videofightdao"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_video_fight"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/channelfightdao"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_channel_fight"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/chpopulardao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/compereinfodao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/culifdao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/gamecenterdao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/livehelperdao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/webdbdao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/zhuiwanrecdatadao"
	"git.yy.com/server/jiaoyou/go_projects/api/common/constinfo"
	"git.yy.com/server/jiaoyou/go_projects/api/common/notify/wchat"
	"git.yy.com/server/jiaoyou/go_projects/api/common/util"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/common"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/contract"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_ch_popular"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_live_helper"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/zhuiwan_rec_data"
	"git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/config"
	mgodao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/mgodao/v2"
	redisdao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/redisdao/v2"
)

const (
	// 10000以下的为固定id

	// 100: 追玩-端游开黑
	// 300: 追玩-上下滑动分流推荐
	// 500: 追玩-萌新主持
	// 700: 追玩-附近直播
	// 900: 追玩-订阅
	// 1000: 追玩-热门
	// 1100: 追玩-猜你喜欢-交友房
	// 1200: 追玩-猜你喜欢-约战房
	// 1300: 追玩-猜你喜欢-宝贝房
	// 1400: 追玩-猜你喜欢-语音房
	// 1500: 追玩-猜你喜欢-YY频道
	// 1800: 娱乐-上下滑
	// 1900: 技能卡-上下滑
	// 2000: Yo-热门
	// 2100: Yo-猜你喜欢-交友房
	// 2200: Yo-猜你喜欢-约战房
	// 2300: Yo-上下滑动分流推荐
	// 2400: Yo-猜你喜欢-语音房
	// 2500: Yo-猜你喜欢-YY频道
	// 2600: Yo-猜你喜欢-宝贝房
	// 2700: Yo-端游开黑
	// 2900: Yo-发现页-订阅
	// 2900: Yo-首页-订阅
	// 3000: Yo开黑-热门
	// 3100: Yo开黑-猜你喜欢-交友房
	// 3200: Yo开黑-猜你喜欢-约战房
	// 3400: Yo开黑-猜你喜欢-语音房
	// 3500: Yo开黑-猜你喜欢-YY频道
	// 3600: Yo开黑-猜你喜欢-宝贝房
	// 4000: Yo-人气推荐
	// 4100: Yo-小游戏
	// 5000: yaya-人气推荐
	// 5100: yaya开黑-猜你喜欢-交友房
	// 5200: yaya开黑-猜你喜欢-约战房
	// 5400: yaya开黑-猜你喜欢-语音房
	// 5500: yaya开黑-猜你喜欢-YY频道
	// 5600: yaya开黑-猜你喜欢-宝贝房
	// 8000: 语音房-更多精彩，补量数据
	// 8100: 互动秀-更多精彩，补量数据
	// 8200: 语音房-退出房间，补量数据
	// 8300: 互动秀-退出房间，补量数据
	// 8400: yo语音搜索推荐房间，补量数据
	// 8500: yo交友搜索推荐房间，补量数据

	// [9000, 9900] banner活动类已占用 若下限冲突需迁移
	bannerOtherCategoryIDStart = 9000
	bannerOtherCategoryIDEnd   = 9900

	terminalIDStep = 10000 // 每个应用相隔的id
	tabIDStep      = 100   // 每个tab id预留100个位

	businessZhuiWan = 301

	moduleStartID = 1000000 // 插入模块起始id
)

// 导航配置-推荐逻辑 (新增枚举后需要修改函数 isValidRecommendLogic)
const (
	recommendByConfig              = 1  // 按运营配置推荐
	recommendByJYOnlineInfo        = 2  // 推荐交友所有的在线主持
	recommendByPKOnlineInfo        = 3  // 推荐约战所有的在线主持
	recommendByBabyOnlineInfo      = 4  // 推荐宝贝所有的在线主持
	recommendHitHotInfo            = 5  // 热门推荐
	recommendByWeight              = 6  // 按权重的大小排序
	recommendVideoDatingGameType   = 7  // 派对-多人视频玩法推荐
	recommendGameTeam              = 8  // 开黑组队
	recommendChatRoom              = 9  // 扩列聊天
	recommendVideo                 = 10 // 视频推荐
	recommendAudio                 = 11 // 音频推荐
	recommendNewCompere            = 12 // 萌新主持推荐
	recommendHonorOfKings          = 13 // 王者荣耀
	recommendPUBGMobile            = 14 // 和平精英
	recommendTeamFightTactics      = 15 // 云顶之弈
	recommendMiniGame              = 16 // 小游戏
	recommendBySupplement          = 17 // 补量系统推荐
	recommendReception             = 18 // 接待上麦
	recommendAllOnlineInfo         = 19 // 推荐所有的在线主持
	recommendSkillCard             = 20 // 技能卡
	recommendByJYAndBabyOnlineInfo = 21 // 推荐交友、宝贝所有的在线主持
	recommendGameQueue             = 22 // 开黑车队
	recommendUgcOnline             = 23 // 位置-UGC房
	recommendUgcKaihei             = 24 // 位置+开黑组队
	recommendUgcChat               = 25 // 位置+闲聊唠嗑
	recommendUgcGame               = 26 // 位置+趣味游戏
	recommendUgcWzry               = 27 // 位置+王者荣耀
	recommendUgcPubg               = 28 // 位置+和平精英
	recommendUgcJcczz              = 29 // 位置+金铲铲之战
)

// gameID, (相当于二级标签,追玩方面定义)
const (
	// 云顶之弈29
	gameIDYDZY = 29
	// 男神192, 女神193,
	gameIDGood    = 192
	gameIDGoddess = 193
	// 王者荣耀：正式环境172/135, 测试环境138
	gameIDProdWZRY  = 172
	gameIDProdWZRY2 = 135
	gameIDTestWZRY  = 138
	// 和平精英: 正式环境136/123, 测试环境187
	gameIDProdHPJY  = 136
	gameIDProdHPJY2 = 123
	gameIDTestHPJY  = 187
)

// playType, (相当于一级标签,追玩方面定义)
const (
	playTypeGameTeam  = 1 // 开黑组队
	playTypeCloudGame = 2 // 云游戏
	playTypeChatRoom  = 3 // 扩列交友
	playTypeMiniGame  = 4 // 小游戏
)

type onlineInfoType int

// 频道类型: 视频或语音
const (
	onlineInfoAll onlineInfoType = iota
	onlineInfoVideo
	onlineInfoAudio
	onlineInfoGame // 开黑车队
)

type onlineInfo struct {
	m map[Channel]*onlineChannelInfo
}

type onlineInfoCategory struct {
	all   onlineInfo
	video onlineInfo // 视频开播
	audio onlineInfo // 音频开播
}

type tabFilter func(c Channel, oi *onlineInfo) bool

type recommendMaterials struct {
	platform            int
	items               []*mgodao.RecommendItem
	conf                *mgodao.RecommendTabConfig
	oic                 *onlineInfoCategory
	t                   onlineInfoType
	businessList        []int
	filter              tabFilter
	recommendOnlineInfo bool
	waitingTeamList     []waitingTeamInfo // 开黑车队匹配中列表
}

func (r *recommendMaterials) oi() *onlineInfo {
	return r.oic.getOnlineInfo(r.t)
}

// 判断gameID是否为王者荣耀
func isGameIDMatchWZRY(gameID int) bool {
	if config.ServerConfig.IsTestEnv {
		return gameID == gameIDTestWZRY
	}
	return gameID == gameIDProdWZRY || gameID == gameIDProdWZRY2
}

// 判断gameID是否为和平精英
func isGameIDMatchHPJY(gameID int) bool {
	if config.ServerConfig.IsTestEnv {
		return gameID == gameIDTestHPJY
	}
	return gameID == gameIDProdHPJY || gameID == gameIDProdHPJY2
}

// 判断gameID是否为云顶之弈
func isGameIDMatchYDZY(gameID int) bool {
	return gameID == gameIDYDZY
}

// 判断gameID是否男神或女神
func isGameIDMatchGoodOrGoddess(gameID int) bool {
	return gameID == gameIDGoddess || gameID == gameIDGood
}

// 获取指定业务类型的在线信息列表
func (p *onlineInfoCategory) getOnlineInfo(t onlineInfoType) *onlineInfo {
	switch t {
	case onlineInfoAll:
		return &p.all
	case onlineInfoVideo:
		return &p.video
	case onlineInfoAudio:
		return &p.audio
	default:
		return &p.all
	}
}

func (o *onlineInfo) setOnlineInfoOne(info *onlineChannelInfo, business int) {
	if info == nil || info.UID == 0 || info.Sid == 0 || info.Ssid == 0 {
		return
	}
	info.Business = business
	if o.m == nil {
		o.m = make(map[Channel]*onlineChannelInfo)
	}
	c := Channel{Sid: info.Sid, Ssid: info.Ssid}
	o.m[c] = info
	yylog.Debug("set online info", zap.Int("business", business), zap.Any("info", info))
}

func (o *onlineInfo) setOnlineInfo(info []*onlineChannelInfo, business int) {
	if len(info) == 0 {
		return
	}
	for _, it := range info {
		o.setOnlineInfoOne(it, business)
	}
}

// 获取各个业务id的在线频道数量: businessID->channelNumbers
func (o *onlineInfo) getBusinessCount() map[int]int {
	if o == nil {
		return nil
	}
	m := make(map[int]int)
	for _, info := range o.m {
		m[info.Business]++
	}
	return m
}

func (o *onlineInfo) getGameTypeCount() map[int]int64 {
	if o == nil {
		return nil
	}
	m := make(map[int]int64)
	for _, info := range o.m {
		m[info.GameType]++
	}
	return m
}

func (o *onlineInfo) getChannelCountWithBusiness(business int) int64 {
	if o == nil {
		return 0
	}
	var count int64
	for _, info := range o.m {
		if info.Business == business {
			count++
		}
	}
	return count
}

func (o *onlineInfo) getUID(c Channel) int64 {
	if o == nil {
		return 0
	}
	if c.Sid == 0 || c.Ssid == 0 {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.UID
}

func (o *onlineInfo) getIcon(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.Icon
}

func (o *onlineInfo) getBigIcon(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.BigIcon
}

func (o *onlineInfo) getPlayType(c Channel) int {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.PlayType
}

// 根据主持uid获取其所在频道
func (o *onlineInfo) getChannel(uid int64) Channel {
	if o == nil {
		return Channel{}
	}
	if uid == 0 {
		return Channel{}
	}
	for _, info := range o.m {
		if info.UID == uid {
			return Channel{Sid: info.Sid, Ssid: info.Ssid}
		}
	}
	return Channel{}
}

func (o *onlineInfo) getGameType(c Channel) int {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.GameType
}

func (o *onlineInfo) getBusiness(c Channel) int {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.Business
}

func (o *onlineInfo) getRoomType(c Channel) int {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.RoomType
}

func (o *onlineInfo) getRoomClass(c Channel) int {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.RoomClass
}

func (o *onlineInfo) getTerminal(c Channel) []int {
	if o == nil {
		return nil
	}
	info, ok := o.m[c]
	if !ok {
		return nil
	}
	return info.Terminal
}

// 获取指定业务的所有开播uid, 确保uid不重复
func (o *onlineInfo) getUIDListByBusinessList(businessList []int) []int64 {
	if o == nil {
		return nil
	}
	in := func(business int) bool {
		if len(businessList) == 0 { // 所有业务
			return true
		}
		for _, b := range businessList {
			if business == b {
				return true
			}
		}
		return false
	}
	var uidList []int64
	uidSet := make(map[int64]bool)
	for _, info := range o.m {
		if !in(info.Business) {
			continue
		}
		if uidSet[info.UID] {
			continue
		}
		uidSet[info.UID] = true
		uidList = append(uidList, info.UID)
	}
	return uidList
}

func (o *onlineInfo) getGameID(c Channel) int {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.GameID
}

func (o *onlineInfo) getNick(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.Nick
}

func (o *onlineInfo) getRoomName(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.RoomName
}

func (o *onlineInfo) getRoomCover(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.RoomCover
}

func (o *onlineInfo) getRoomNo(c Channel) int64 {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.RoomNo
}

func (o *onlineInfo) getSubRoomClass(c Channel) int {
	if o == nil {
		return 0
	}
	info, ok := o.m[c]
	if !ok {
		return 0
	}
	return info.SubRoomClass
}

func (o *onlineInfo) getLayoutConfig(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.LayoutConfig
}

func (o *onlineInfo) getAttributesStatus(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.AttributesStatus
}

func (o *onlineInfo) getRcAttributesStatus(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.RcAttributesStatus
}

func (o *onlineInfo) getSideAttributesStatus(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.SideAttributesStatus
}

func (o *onlineInfo) getGameName(c Channel) string {
	if o == nil {
		return ""
	}
	info, ok := o.m[c]
	if !ok {
		return ""
	}
	return info.GameName
}

func (o *onlineInfo) getGuestList(c Channel) []guestInfo {
	if o == nil {
		return []guestInfo{}
	}
	info, ok := o.m[c]
	if !ok {
		return []guestInfo{}
	}
	return append([]guestInfo{}, info.OnlineList...)
}

func (o *onlineInfo) getVsChannel(c Channel) Channel {
	if o == nil {
		return Channel{}
	}
	info, ok := o.m[c]
	if !ok {
		return Channel{}
	}
	return Channel{Sid: info.VsSid, Ssid: info.VsSsid}
}

func (o *onlineInfo) getChannelGuest(c Channel) []int64 {
	if o == nil {
		return nil
	}
	info, ok := o.m[c]
	if !ok {
		return nil
	}
	return info.Guest
}

func (o *onlineInfo) getChannel2Uid() map[Channel]int64 {
	if o == nil {
		return nil
	}
	m := make(map[Channel]int64)
	for c, info := range o.m {
		m[c] = info.UID
	}
	return m
}

// position为0：表示自动推荐的配置
// position不为0：表示对应tab的推荐位置
func getTabID(zoneID, position int64) int64 {
	return zoneID - position
}

func getRecommendItem() (map[int]map[int64][]*mgodao.RecommendItem, error) {
	recommendManageConfigList, err := mgodao.GetRecommendManageConfig(0, 0, 0, 0)
	if err != nil {
		return nil, err
	}
	ret := make(map[int]map[int64][]*mgodao.RecommendItem)
	ret[platformAndroid] = make(map[int64][]*mgodao.RecommendItem)
	ret[platformIOS] = make(map[int64][]*mgodao.RecommendItem)
	ret[platformPC] = make(map[int64][]*mgodao.RecommendItem)
	for _, recommendManageConfigItem := range recommendManageConfigList {
		items, err := mgodao.GetRecommendItem(recommendManageConfigItem.ID, 0, 0)
		if err != nil {
			yylog.Error("cannot get recommend item", zap.String("id", recommendManageConfigItem.ID), zap.Error(err))
			continue
		}
		id := recommendManageConfigItem.ZoneID
		for _, position := range recommendManageConfigItem.PositionList {
			for _, item := range items {
				itemTemp := item
				if recommendManageConfigItem.Regular == topListRegularName {
					continue
				}
				if itemTemp.RecommendStartTime > 0 && itemTemp.RecommendTimeExpired(time.Now().Unix()) {
					continue
				}
				if !itemTemp.IsRecommendStatus() {
					continue
				}
				itemTemp.Position = position
				itemTemp.ZoneID += position
				itemTemp.ConfZoneID = id
				switch itemTemp.Platform {
				case platformAndroid:
					ret[platformAndroid][id] = append(ret[platformAndroid][id], &itemTemp)
				case platformIOS:
					ret[platformIOS][id] = append(ret[platformIOS][id], &itemTemp)
				case platformAndroidAndIOS:
					ret[platformAndroid][id] = append(ret[platformAndroid][id], &itemTemp)
					ret[platformIOS][id] = append(ret[platformIOS][id], &itemTemp)
				case platformPC:
					ret[platformPC][id] = append(ret[platformPC][id], &itemTemp)
				case platformPCAndAPP:
					ret[platformAndroid][id] = append(ret[platformAndroid][id], &itemTemp)
					ret[platformIOS][id] = append(ret[platformIOS][id], &itemTemp)
					ret[platformPC][id] = append(ret[platformPC][id], &itemTemp)
				}
				yylog.Debug("recommend itemTemp", zap.Int64("id", id),
					zap.Int64s("positionList", recommendManageConfigItem.PositionList),
					zap.Int64("position", position), zap.Any("itemTemp", itemTemp))
			}
		}
	}
	return ret, nil
}

func getRecommendJYList() (map[int]map[int64][]*mgodao.RecommendItem, error) {
	recommendList, err := mgodao.GetRecommendJYList()
	if err != nil {
		return nil, err
	}
	recommendManageConfigList, err := mgodao.GetRecommendManageConfig(0, 0, 0, 0)
	if err != nil {
		return nil, err
	}
	recommendManageConfigMap := make(map[int64][]mgodao.RecommendManageConfig)
	for _, c := range recommendManageConfigList {
		recommendManageConfigMap[c.ZoneID] = append(recommendManageConfigMap[c.ZoneID], c)
	}

	ret := make(map[int]map[int64][]*mgodao.RecommendItem)
	ret[platformAndroid] = make(map[int64][]*mgodao.RecommendItem)
	ret[platformIOS] = make(map[int64][]*mgodao.RecommendItem)
	ret[platformPC] = make(map[int64][]*mgodao.RecommendItem)
	for _, item := range recommendList {
		if item.RecommendStartTime > 0 && item.RecommendTimeExpired(time.Now().Unix()) {
			continue
		}
		if item.ZoneID != topListTabID {
			recommendItem := mgodao.RecommendItem{
				UID:        item.UID,
				Sid:        item.Sid,
				Ssid:       item.Ssid,
				ZoneID:     item.ZoneID,
				ConfZoneID: item.ZoneID,
				Weight:     item.Weight,
				Business:   businessJY,
				Platform:   platformPCAndAPP,
				Mode:       item.Mode,
			}
			yylog.Debug("recommend item", zap.Any("item", recommendItem))
			ret[platformAndroid][item.ZoneID] = append(ret[platformAndroid][item.ZoneID], &recommendItem)
			ret[platformIOS][item.ZoneID] = append(ret[platformIOS][item.ZoneID], &recommendItem)
			ret[platformPC][item.ZoneID] = append(ret[platformPC][item.ZoneID], &recommendItem)
		} else {
			for _, configItem := range recommendManageConfigMap[item.ZoneID] {
				if configItem.Regular != topListRegularName {
					continue
				}
				for _, position := range configItem.PositionList {
					if position == 0 {
						continue
					}
					recommendItem := mgodao.RecommendItem{
						UID:               item.UID,
						Sid:               item.Sid,
						Ssid:              item.Ssid,
						ZoneID:            item.ZoneID + position,
						ConfZoneID:        item.ZoneID,
						Weight:            item.Weight,
						Business:          businessJY,
						Platform:          platformPCAndAPP,
						Mode:              item.Mode,
						Position:          position,
						RecommendManageID: configItem.ID,
					}
					yylog.Debug("recommend item", zap.Int64("id", item.ZoneID), zap.Any("item", recommendItem))
					ret[platformAndroid][item.ZoneID] = append(ret[platformAndroid][item.ZoneID], &recommendItem)
					ret[platformIOS][item.ZoneID] = append(ret[platformIOS][item.ZoneID], &recommendItem)
					ret[platformPC][item.ZoneID] = append(ret[platformPC][item.ZoneID], &recommendItem)
				}
			}
		}
	}
	return ret, nil
}

// 获取每个tab对应的推荐配置： 平台->tabID->推荐列表
func getAllTabRecommendItem() (map[int]map[int64][]*mgodao.RecommendItem, error) {
	m1, err := getRecommendItem()
	if err != nil {
		return nil, err
	}
	m2, err := getRecommendJYList()
	if err != nil {
		return nil, err
	}
	merge := func(m1, m2 map[int]map[int64][]*mgodao.RecommendItem) map[int]map[int64][]*mgodao.RecommendItem {
		if m1 == nil {
			return m2
		}
		for platform, recommendItem := range m2 {
			for id, items := range recommendItem {
				m1[platform][id] = append(m1[platform][id], items...)
			}
		}
		return m1
	}
	return merge(m1, m2), nil
}

// 获取打通房相关的在线信息
// 配置的频道有人则认为在线、在线的uid使用ow uid
func getConfigBusinessBasicOnlineChannelInfo() ([]*onlineChannelInfo, error) {
	items, err := mgodao.GetRecommendItem("", 0, businessBasic)
	if err != nil {
		return nil, err
	}
	var culChannels []culifdao.CULChannel
	var channels []Channel
	for _, item := range items {
		if item.Sid == 0 || item.Ssid == 0 {
			continue
		}
		culChannels = append(culChannels, culifdao.CULChannel{
			Topsid: item.Sid,
			Subsid: item.Ssid,
		})
		channels = append(channels, Channel{
			Sid:  item.Sid,
			Ssid: item.Ssid,
		})
	}
	ret, err := culifdao.BatchGetSubchannelUserCount(culChannels)
	if err != nil {
		return nil, err
	}
	infoMap, err := batchGetGameChannelOnlineInfoField(channels)
	if err != nil {
		return nil, err
	}
	var onlineChannelInfo []*onlineChannelInfo
	var sidList []int64
	for _, channel := range channels {
		c := culifdao.CULChannel{Topsid: channel.Sid, Subsid: channel.Ssid}
		count, _ := ret[c]
		if c.Topsid == 0 || c.Subsid == 0 || count == 0 {
			yylog.Warn("online count", fts.SSID(c.Topsid), fts.SID(c.Topsid), zap.Int64("count", count))
			continue
		}
		info, _ := infoMap[channel]
		if info == nil {
			continue
		}
		info.Terminal = allTerminal
		onlineChannelInfo = append(onlineChannelInfo, info)
		sidList = append(sidList, c.Topsid)
		yylog.Debug("online info", fts.SSID(c.Topsid), fts.SID(c.Subsid), zap.Int64("count", count), zap.String("info", info.String()))
	}
	ow, err := webdbdao.BatchGetSidsOwnerUid(sidList)
	if err != nil {
		return nil, err
	}
	for i, o := range onlineChannelInfo {
		if uid, ok := ow[o.Sid]; ok && uid > 0 {
			onlineChannelInfo[i].UID = uid
		}
		yylog.Debug("basic online channel info", zap.String("info", onlineChannelInfo[i].String()))
	}
	return onlineChannelInfo, nil
}

// 获取PC陪玩相关的在线信息
// 首麦有人则认为在线、在线的uid使用首麦用户uid
func getConfigBusinessPWOnlineChannelInfo() ([]*onlineChannelInfo, error) {
	items, err := mgodao.GetRecommendItem("", 0, businessPW)
	if err != nil {
		return nil, err
	}
	var channels []common.ChannelId
	for _, item := range items {
		if item.Sid == 0 || item.Ssid == 0 {
			continue
		}
		if !isAPPTerminal(item.Terminal) {
			continue
		}
		channels = append(channels, common.ChannelId{
			Sid:  item.Sid,
			Ssid: item.Ssid,
		})
		yylog.Debug("online channel info", fts.SSID(item.Sid), fts.SID(item.Ssid))
	}

	ret, err := culifdao.GetFirstMaixu(channels)
	if err != nil {
		return nil, err
	}
	var onlineChannelInfoList []*onlineChannelInfo
	for c, uid := range ret {
		if c.Sid > 0 && c.Ssid > 0 && uid > 0 {
			onlineChannelInfoList = append(onlineChannelInfoList, &onlineChannelInfo{
				Sid:      c.Sid,
				Ssid:     c.Ssid,
				UID:      uid,
				Terminal: allTerminal,
			})
		}
		yylog.Debug("online channel info", fts.SSID(c.Sid), fts.SID(c.Ssid), fts.UID(uid))
	}
	return onlineChannelInfoList, nil
}

func getVSMap(ctx context.Context, channelFightList []*fts_channel_fight.Channel,
	videoFightChannelList []*fts_video_fight.VideoFightCh, newVideoFightChList []*hdgame.ChannelId) (map[Channel]match, error) {
	vsMap := make(map[Channel]match)
	// 乱斗本方、对方信息
	cfRet, err := channelfightdao.BatchGetChannelFightVsInfo(channelFightList)
	if err != nil || cfRet == nil || cfRet.Ret != 0 {
		return nil, err
	}
	if cfRet != nil {
		for _, info := range cfRet.Info {
			if info == nil {
				continue
			}
			c := Channel{
				Sid:  info.Sid,
				Ssid: info.Ssid,
			}
			vsMap[c] = match{
				Sid:  info.VsSid,
				Ssid: info.VsSsid,
				UID:  info.VsCompere,
			}
		}
	}

	// 对人乱斗本方、对方信息
	vfRet, err := videofightdao.BatchGetVideoFightInfo(&fts_video_fight.BatchGetVideoFightInfoReq{Chs: videoFightChannelList})
	if err != nil || vfRet == nil || vfRet.Ret != 0 {
		yylog.Error("cannot batch get video fight info", fts.TraceID(ctx), zap.Any("ret", cfRet), zap.Error(err))
	}
	if vfRet != nil {
		for _, info := range vfRet.Info {
			if info == nil {
				continue
			}
			c := Channel{
				Sid:  info.Sid,
				Ssid: info.Ssid,
			}
			vsMap[c] = match{
				Sid:  info.MatchSid,
				Ssid: info.MatchSsid,
				UID:  info.MatchUid,
			}
		}
	}

	// 新多人乱斗
	if len(newVideoFightChList) > 0 {
		var rsp *hdgame.BatchGetCrossPkInfoRsp
		rsp, err = hdgamedao.Client().BatchGetCrossPkInfo(ctx, &hdgame.BatchGetCrossPkInfoReq{
			RoomType:    datingRoomType,
			ChannelList: newVideoFightChList,
		})
		if err != nil {
			yylog.Error("cannot batch get new video fight info", fts.TraceID(ctx), zap.Any("ret", rsp), zap.Error(err))
		}
		if len(rsp.PkInfoList) > 0 {
			for _, info := range rsp.PkInfoList {
				if info == nil {
					continue
				}
				c := Channel{Sid: info.Sid, Ssid: info.Ssid}
				vsMap[c] = match{
					Sid:  info.MatchSid,
					Ssid: info.MatchSsid,
					UID:  info.MatchUid,
				}
			}
		}
	}

	return vsMap, nil
}

func getVideoFightGuest(videoFightChannelList []*fts_video_fight.VideoFightCh,
	videoDatingChannelList []*fts_video_dating.ChInfo) (map[Channel][]int64, error) {
	// 多人乱斗嘉宾
	guest := make(map[Channel][]int64)
	vfGuestRet, err := videofightdao.BatchGetVideoFightGuestCharm(&fts_video_fight.BatchGetVideoFightGuestCharmReq{Chs: videoFightChannelList})
	if err != nil || vfGuestRet == nil || vfGuestRet.Ret != 0 {
		return guest, nil
	}
	if vfGuestRet != nil && len(vfGuestRet.ChGuests) == len(videoFightChannelList) {
		for i, info := range vfGuestRet.ChGuests {
			c := Channel{
				Sid:  videoFightChannelList[i].Sid,
				Ssid: videoFightChannelList[i].Ssid,
			}
			for _, g := range info {
				guest[c] = append(guest[c], g.UID)
			}
			yylog.Debug("batch get video fight guest", zap.Any("c", c), zap.Any("info", info))
		}
	}

	// 多人视频嘉宾
	vdGuestRet, err := videodatingdao.BatchGetVideoDatingInfo(videoDatingChannelList)
	if err != nil || vdGuestRet == nil || vdGuestRet.Resp == nil || vdGuestRet.Resp.Ret != 0 {
		yylog.Error("cannot batch get video dating info", zap.Any("ret", vdGuestRet), zap.Error(err))
		return guest, nil
	}
	for _, info := range vdGuestRet.InfoList {
		if info == nil {
			continue
		}
		c := Channel{
			Sid:  info.Sid,
			Ssid: info.Ssid,
		}
		for _, g := range info.GetGuestList() {
			guest[c] = append(guest[c], g.UID)
		}
	}
	return guest, nil
}

func batchGetGameActInfoMap(gameRoleChannels []*fts_game_role.ChInfo) (map[fts_game_role.ChInfo]*fts_game_role.ActInfo, error) {
	if len(gameRoleChannels) == 0 {
		return map[fts_game_role.ChInfo]*fts_game_role.ActInfo{}, nil
	}
	batchGetGameActInfoReq := fts_game_role.BatchGetGameActInfoReq{
		Chs: gameRoleChannels,
	}
	batchGetGameActInfoResp, err := gameroledao.BatchGetGameActInfo(&batchGetGameActInfoReq)
	if err != nil {
		return nil, err
	}
	if batchGetGameActInfoResp == nil {
		return nil, nil
	}
	if len(gameRoleChannels) != len(batchGetGameActInfoResp.Acts) {
		return nil, nil
	}
	roleInfoMap := make(map[fts_game_role.ChInfo]*fts_game_role.ActInfo, len(gameRoleChannels))
	for i := range batchGetGameActInfoResp.Acts {
		c := fts_game_role.ChInfo{
			Sid:  gameRoleChannels[i].Sid,
			Ssid: gameRoleChannels[i].Ssid,
		}
		roleInfoMap[c] = batchGetGameActInfoResp.Acts[i]
	}
	return roleInfoMap, nil
}

func refreshGuestInfo() {
	ctx, span := fts.StartNewTrace(nil)
	defer span.End()
	onlineCompere, err := gamecenterdao.GetAllOnlineCompere()
	if err != nil || onlineCompere == nil || onlineCompere.Ret != 0 {
		yylog.Error("failed get all online compere", fts.TraceID(ctx), zap.Error(err))
		return
	}
	var gameRoleChannels []*fts_game_role.ChInfo
	var channels []*fts_game_center.ChInfo
	for _, r := range onlineCompere.Info {
		if r == nil {
			continue
		}
		if r.Sid == 0 || r.Ssid == 0 || r.UID == 0 {
			continue
		}
		yylog.Debug("jy online channel info", fts.TraceID(ctx), fts.SSID(r.Sid), fts.SID(r.Ssid), fts.UID(r.UID))
		gameRoleChannels = append(gameRoleChannels, &fts_game_role.ChInfo{
			Sid:  r.Sid,
			Ssid: r.Ssid,
		})
		channels = append(channels, &fts_game_center.ChInfo{Sid: r.Sid, Ssid: r.Ssid})
	}
	gameInfo, err := gamecenterdao.BatchGetGameInfoMap(channels)
	if err != nil {
		yylog.Error("failed get game info", fts.TraceID(ctx), zap.Error(err))
		return
	}
	jyGuestInfoList := getJYGuestInfoListV2(ctx, gameRoleChannels, gameInfo)
	if jyGuestInfoList == nil {
		return
	}
	guestInfoMap := make(map[string]string)
	for _, channel := range gameRoleChannels {
		jyGuestInfo := jyGuestInfoList(*channel)
		b, err := json.Marshal(jyGuestInfo)
		if err != nil {
			yylog.Error("failed marshal json", fts.TraceID(ctx), zap.Error(err), zap.Any("jyGuestInfo", jyGuestInfo))
			continue
		}
		guestInfoMap[toChannelStr(channel.Sid, channel.Ssid)] = string(b)
	}
	if err := redisdao.SetGuestInfo(guestInfoMap); err != nil {
		yylog.Error("failed set guest info", fts.TraceID(ctx), zap.Error(err))
		return
	}
}

// deprecated see getJYGuestInfoListV2
func getJYGuestInfoList(gameRoleChannels []*fts_game_role.ChInfo) func(gameRoleChannel fts_game_role.ChInfo) []guestInfo {
	if len(gameRoleChannels) == 0 {
		return nil
	}
	roleInfoMap, err := batchGetGameActInfoMap(gameRoleChannels)
	if err != nil {
		yylog.Error("failed get game act info", zap.Error(err))
		return nil
	}

	var guestUidList []int64
	for _, roleInfo := range roleInfoMap {
		if roleInfo == nil {
			continue
		}
		for _, guest := range roleInfo.Guests {
			guestUidList = append(guestUidList, guest.UID)
		}
	}
	userInfoMap := userinfodao.RepeatedBatchGetUserInfoByType(context.Background(), guestUidList, []string{"nick", "avatar_info", "sex"})
	webdbInfoMap, err := webdbdao.BatchGetYYNickLogo(guestUidList)
	if err != nil {
		yylog.Error("", zap.Error(err), zap.Int("size", len(guestUidList)))
	}

	return func(gameRoleChannel fts_game_role.ChInfo) []guestInfo {
		var guestInfoList []guestInfo
		roleInfo, _ := roleInfoMap[gameRoleChannel]
		if roleInfo == nil {
			return nil
		}
		for _, guest := range roleInfo.Guests {
			if guest.UID == 0 {
				continue
			}
			userInfo, _ := userInfoMap[guest.UID]
			var nick, avatar string
			var sex int32
			if userInfo != nil {
				nick = userInfo.Nick
				avatar = userInfo.GetAvatarInfo().GetURL()
				sex = userInfo.Sex
			}
			info := guestInfo{
				UID:    guest.UID,
				Nick:   nick,
				Avatar: avatar,
				Gender: sex,
				Seat:   guest.Pos,
			}
			if v, exist := webdbInfoMap[guest.UID]; exist {
				info.Nick = v.Nick
				info.Gender = int32(v.Sex)
				info.AvatarHD = v.Logo100    // 先取gif
				if len(info.AvatarHD) == 0 { // gif的图不存在，再取640的
					info.AvatarHD = v.Logo640
				}
			}
			yylog.Debug("guest info", zap.Any("channel", gameRoleChannel), zap.Any("info", info))
			guestInfoList = append(guestInfoList, info)
		}
		return guestInfoList
	}
}

func getJYGuestInfoListV2(ctx context.Context, channels []*fts_game_role.ChInfo,
	gameInfoMap map[fts_game_center.ChInfo]*fts_game_center.GameInfo) func(gameRoleChannel fts_game_role.ChInfo) []guestInfo {

	if len(channels) == 0 {
		return nil
	}
	var gameRoleChannels []*fts_game_role.ChInfo
	// 新多人玩法
	var newGame2chList = map[int64][]*hdgame.ChannelId{}
	for _, channel := range channels {
		c := fts_game_center.ChInfo{Sid: channel.Sid, Ssid: channel.Ssid}
		if gi, ok := gameInfoMap[c]; ok && gi != nil && isHdgameGameType(int(gi.GameType)) {
			// 新玩法
			newGame2chList[gi.GameType] = append(newGame2chList[gi.GameType], &hdgame.ChannelId{Sid: channel.Sid, Ssid: channel.Ssid})
		} else {
			gameRoleChannels = append(gameRoleChannels, channel)
		}
	}

	// 频道对应嘉宾列表
	var ch2guests = map[fts_game_role.ChInfo][]guestInfo{}
	var guestUidList []int64

	// 非新玩法的，去hdgame 查询嘉宾信息
	guestUidList, ch2guests = batchGetGuestByGameRoleSvc(ctx, guestUidList, ch2guests, gameRoleChannels)

	// 查询新玩法的嘉宾信息
	guestUidList, ch2guests = batchGetGuestByHgameSvc(ctx, guestUidList, ch2guests, newGame2chList)

	userInfoMap := userinfodao.RepeatedBatchGetUserInfoByType(context.Background(), guestUidList, []string{"nick", "avatar_info", "sex"})
	webdbInfoMap, err := webdbdao.BatchGetYYNickLogo(guestUidList)
	if err != nil {
		yylog.Error("", fts.TraceID(ctx), zap.Error(err), zap.Int("size", len(guestUidList)))
	}

	if len(ch2guests) == 0 {
		return func(gameRoleChannel fts_game_role.ChInfo) []guestInfo {
			return nil
		}
	}

	// 填充嘉宾的头像昵称性别
	for ch, guests := range ch2guests {
		if len(guests) == 0 {
			continue
		}
		for i, guest := range guests {
			if guest.UID == 0 {
				continue
			}
			userInfo, _ := userInfoMap[guest.UID]
			if userInfo != nil {
				guests[i].Nick = userInfo.Nick
				guests[i].Avatar = userInfo.GetAvatarInfo().GetURL()
				guests[i].Gender = userInfo.Sex
			}
			if v, exist := webdbInfoMap[guest.UID]; exist {
				guests[i].Nick = v.Nick
				guests[i].Gender = int32(v.Sex)
				guests[i].AvatarHD = v.Logo100    // 先取gif
				if len(guests[i].AvatarHD) == 0 { // gif的图不存在，再取640的
					guests[i].AvatarHD = v.Logo640
				}
			}
		}
		c := fts_game_center.ChInfo{Sid: ch.Sid, Ssid: ch.Ssid}
		var gameType int64
		if gi, ok := gameInfoMap[c]; ok && gi != nil {
			gameType = gi.GameType
		}
		yylog.Debug("jy_guest_info", fts.TraceID(ctx), fts.SID(ch.Sid), fts.SSID(ch.Ssid),
			zap.Int64("gameType", gameType), zap.Int("count", len(guests)), zap.Any("info", guests))
	}

	return func(gameRoleChannel fts_game_role.ChInfo) []guestInfo {
		var guestList = ch2guests[gameRoleChannel]
		if len(guestList) == 0 {
			return nil
		}
		return guestList
	}
}

func batchGetGuestByHgameSvc(ctx context.Context,
	guestUidList []int64,
	ch2guests map[fts_game_role.ChInfo][]guestInfo,
	newGame2chList map[int64][]*hdgame.ChannelId) (
	rGuestUidList []int64, rCh2guests map[fts_game_role.ChInfo][]guestInfo) {
	if len(newGame2chList) == 0 {
		return guestUidList, ch2guests
	}
	for gameType, chList := range newGame2chList {
		var req = &hdgame.BatchGetChannelGuestReq{
			RoomType: datingRoomType,
			GameType: gameType,
			Channel:  chList,
		}
		rsp, err := hdgamedao.Client().BatchGetChannelGuest(ctx, req)
		if err != nil || rsp == nil {
			yylog.Error("cannot batch get new video fight guest charm", fts.TraceID(ctx), zap.Any("ret", rsp), zap.Error(err))
			continue
		}
		if len(rsp.Info) == 0 {
			continue
		}
		for _, info := range rsp.Info {
			if info == nil || len(info.Guest) == 0 {
				continue
			}
			var guests []guestInfo
			for _, guest := range info.Guest {
				if guest.Uid > 0 {
					guestUidList = append(guestUidList, guest.Uid)
				}
				guests = append(guests, guestInfo{UID: guest.Uid, Seat: guest.Pos})
			}
			ch2guests[fts_game_role.ChInfo{Sid: info.Channel.Sid, Ssid: info.Channel.Ssid}] = guests
			yylog.Debug("hd_game_guest_info", fts.TraceID(ctx), fts.SID(info.Channel.Sid), fts.SSID(info.Channel.Ssid),
				zap.Int("count", len(guests)), zap.Any("guests", guests))
		}
	}
	return guestUidList, ch2guests
}

func batchGetGuestByGameRoleSvc(ctx context.Context,
	guestUidList []int64,
	ch2guests map[fts_game_role.ChInfo][]guestInfo,
	gameRoleChannels []*fts_game_role.ChInfo) (
	rGuestUidList []int64, rCh2guests map[fts_game_role.ChInfo][]guestInfo) {

	if len(gameRoleChannels) == 0 {
		return guestUidList, ch2guests
	}

	// 非新玩法的，去hdgame 查询嘉宾信息
	roleInfoMap, err := batchGetGameActInfoMap(gameRoleChannels)
	if err != nil {
		yylog.Error("failed get game act info", fts.TraceID(ctx), zap.Error(err))
		return
	}

	for c, roleInfo := range roleInfoMap {
		if roleInfo == nil {
			continue
		}
		var guests []guestInfo
		for _, guest := range roleInfo.Guests {
			if guest.UID > 0 {
				guestUidList = append(guestUidList, guest.UID)
				guests = append(guests, guestInfo{UID: guest.UID, Seat: guest.Pos})
			}
		}
		ch2guests[fts_game_role.ChInfo{Sid: c.Sid, Ssid: c.Ssid}] = guests
	}
	return guestUidList, ch2guests
}

// 获取交友相关的在线信息
func getJYOnlineChannelInfo() ([]*onlineChannelInfo, error) {
	var ctx, span = fts.StartNewTrace(nil)
	defer span.End()
	onlineCompere, err := gamecenterdao.GetAllOnlineCompere()
	if err != nil || onlineCompere == nil || onlineCompere.Ret != 0 {
		return nil, err
	}

	var channels []*Channel
	var gameRoleChannels []*fts_game_role.ChInfo
	onlineUids := make(map[Channel]int64)
	for _, r := range onlineCompere.Info {
		if r == nil {
			continue
		}
		if r.Sid == 0 || r.Ssid == 0 || r.UID == 0 {
			continue
		}
		yylog.Debug("jy online channel info", fts.TraceID(ctx), fts.SSID(r.Sid), fts.SID(r.Ssid), fts.UID(r.UID))
		channel := Channel{
			Sid:  r.Sid,
			Ssid: r.Ssid,
		}
		channels = append(channels, &channel)
		onlineUids[channel] = r.UID
		gameRoleChannels = append(gameRoleChannels, &fts_game_role.ChInfo{
			Sid:  r.Sid,
			Ssid: r.Ssid,
		})
	}

	gameInfo, err := gamecenterdao.BatchGetGameInfoMap(channels)
	if err != nil {
		return nil, err
	}

	jyGuestInfoList := getJYGuestInfoListV2(ctx, gameRoleChannels, gameInfo)

	var onlineChannelInfoList []*onlineChannelInfo
	var channelFightList []*fts_channel_fight.Channel
	var videoFightChannelList []*fts_video_fight.VideoFightCh
	var videoDatingChannelList []*fts_video_dating.ChInfo
	var newVideoFightChList []*hdgame.ChannelId
	seen := make(map[int64]bool)
	for channel, uid := range onlineUids {
		info, ok := gameInfo[channel]
		if !ok {
			yylog.Warn("not online", fts.TraceID(ctx), zap.Any("channel", channel), fts.UID(uid))
			continue
		}
		if info == nil {
			continue
		}
		gameType := int(info.GetGameType())
		yylog.Debug("jy online info", fts.TraceID(ctx), zap.Any("channel", channel), fts.UID(uid), zap.Int("gameType", gameType))

		if seen[uid] {
			continue
		}
		seen[uid] = true
		switch gameType {
		case constinfo.KChannelFight:
			channelFightList = append(channelFightList, &fts_channel_fight.Channel{
				Sid:  channel.Sid,
				Ssid: channel.Ssid,
			})
		case constinfo.KVideoFightMatch:
			videoFightChannelList = append(videoFightChannelList, &fts_video_fight.VideoFightCh{
				Sid:  channel.Sid,
				Ssid: channel.Ssid,
			})
		case constinfo.KVideoFightMatchNew:
			newVideoFightChList = append(newVideoFightChList, &hdgame.ChannelId{
				Sid:  channel.Sid,
				Ssid: channel.Ssid,
			})
		case constinfo.KVideoDating, constinfo.KVideoPk:
			videoDatingChannelList = append(videoDatingChannelList, &fts_video_dating.ChInfo{
				Sid:  channel.Sid,
				Ssid: channel.Ssid,
			})
		}
		gameRoleChannel := fts_game_role.ChInfo{
			Sid:  channel.Sid,
			Ssid: channel.Ssid,
		}
		var guestInfoList []guestInfo
		if jyGuestInfoList != nil {
			guestInfoList = jyGuestInfoList(gameRoleChannel)
		}
		var cinfo = &onlineChannelInfo{
			Sid:        channel.Sid,
			Ssid:       channel.Ssid,
			UID:        uid,
			GameType:   gameType,
			RoomType:   gameType2RoomType(gameType),
			RoomClass:  roomClassJY,
			Terminal:   allTerminal,
			OnlineList: guestInfoList,
		}
		onlineChannelInfoList = append(onlineChannelInfoList, cinfo)
	}

	vsMap, err := getVSMap(ctx, channelFightList, videoFightChannelList, newVideoFightChList)
	if err != nil {
		yylog.Error("cannot batch get vs info", fts.TraceID(ctx), zap.Error(err))
	}

	// 多人乱斗嘉宾
	guest, err := getVideoFightGuest(videoFightChannelList, videoDatingChannelList)
	if err != nil {
		yylog.Error("cannot batch get video fight guest", fts.TraceID(ctx), zap.Error(err))
	}

	for i, info := range onlineChannelInfoList {
		if info.GameType == constinfo.KChannelFight ||
			info.GameType == constinfo.KVideoFightMatch ||
			info.GameType == constinfo.KVideoFightMatchNew {

			c := Channel{Sid: info.Sid, Ssid: info.Ssid}
			vs := vsMap[c]
			onlineChannelInfoList[i].VsSid = vs.Sid
			onlineChannelInfoList[i].VsSsid = vs.Ssid
			onlineChannelInfoList[i].VsUID = vs.UID
		}
		// 旧多人玩法
		if isMultiVideoGameType(info.GameType) {
			onlineChannelInfoList[i].Guest = guest[Channel{Sid: info.Sid, Ssid: info.Ssid}]
		}
		// 新玩法
		if isHdgameGameType(info.GameType) {
			// 新玩法, 设置嘉宾
			if len(onlineChannelInfoList[i].OnlineList) > 0 {
				var guestUidList []int64
				for _, g := range onlineChannelInfoList[i].OnlineList {
					guestUidList = append(guestUidList, g.UID)
				}
				onlineChannelInfoList[i].Guest = guestUidList
			}
		}
	}
	return onlineChannelInfoList, nil
}

func onlineChannelInfoRetry(f func() ([]*onlineChannelInfo, error)) func() ([]*onlineChannelInfo, error) {
	return func() ([]*onlineChannelInfo, error) {
		const maxTry = 4
		var err error
		var r []*onlineChannelInfo
		for try := 0; try < maxTry; try++ {
			r, err = f()
			if err != nil {
				if try < maxTry-1 {
					time.Sleep(time.Duration(try+1) * time.Second) // 退避
				}
				continue
			}
			break
		}
		return r, err
	}
}

// 宝贝下线
func getBabyOnlineChannelInfo() ([]*onlineChannelInfo, error) {
	return nil, nil
	// r, err := gametaskdao.QueryOnlineChanel()
	// if err != nil {
	// 	return nil, nil
	// }
	// var ret []*onlineChannelInfo
	// for _, v := range r {
	// 	if v == nil {
	// 		continue
	// 	}
	// 	if v.UID == 0 || v.Sid == 0 || v.Ssid == 0 {
	// 		yylog.Warn("get baby online channel info", fts.SSID(v.Sid), fts.SID(v.Ssid), fts.UID(v.UID))
	// 		continue
	// 	}
	// 	yylog.Debug("get baby online channel info", fts.SSID(v.Sid), fts.SID(v.Ssid), fts.UID(v.UID))
	// 	ret = append(ret, &onlineChannelInfo{
	// 		UID:       v.UID,
	// 		Sid:       v.Sid,
	// 		Ssid:      v.Ssid,
	// 		RoomType:  roomTypeBabyTemplate,
	// 		RoomClass: roomClassBaby,
	// 		Terminal:  allTerminal,
	// 	})
	// }
	// return ret, nil
}

func toChannelStr(sid, ssid int64) string {
	return fmt.Sprintf("%d:%d", sid, ssid)
}

func getJYVideoChannel(info []*onlineChannelInfo) (map[Channel]bool, error) {
	roomList := make([]string, 0, len(info))
	for _, it := range info {
		roomList = append(roomList, toChannelStr(it.Sid, it.Ssid))
	}

	r, err := livehelperdao.BatchCheckRoomVideoOnAll(roomList)
	if err != nil || r == nil {
		return nil, err
	}
	m := make(map[Channel]bool)
	for _, it := range info {
		c := Channel{Sid: it.Sid, Ssid: it.Ssid}
		if _, ok := r.VideoOnMap[toChannelStr(it.Sid, it.Ssid)]; ok {
			m[c] = true
			yylog.Debug("live video", zap.Any("channel", c))
		}
	}
	return m, nil
}

func getPKOrBabyVideoChannel(business int64, info []*onlineChannelInfo) (map[Channel]bool, error) {
	uids := make([]int64, 0, len(info))
	for _, it := range info {
		uids = append(uids, it.UID)
	}
	req := fts_live_helper.CheckVideoLiveReq{
		Business: business,
		UidList:  uids,
	}
	resp, err := livehelperdao.BatchCheckVideoLive(&req)
	if err != nil || resp == nil {
		return nil, err
	}
	m := make(map[Channel]bool)
	for _, it := range info {
		if _, ok := resp.RetMap[it.UID]; ok {
			c := Channel{Sid: it.Sid, Ssid: it.Ssid}
			m[c] = true
			yylog.Debug("live video", zap.Any("channel", c))
		}
	}
	return m, nil
}

func getPKVideoChannel(info []*onlineChannelInfo) (map[Channel]bool, error) {
	return getPKOrBabyVideoChannel(constinfo.BusinessTypePK, info)
}

func getBabyVideoChannel(info []*onlineChannelInfo) (map[Channel]bool, error) {
	return getPKOrBabyVideoChannel(constinfo.BusinessTypeBABY, info)
}

func getVideoChannelRetry(f func([]*onlineChannelInfo) (map[Channel]bool, error)) func([]*onlineChannelInfo) (map[Channel]bool, error) {
	return func(info []*onlineChannelInfo) (map[Channel]bool, error) {
		const maxTry = 4
		var err error
		var r map[Channel]bool
		for try := 0; try < maxTry; try++ {
			r, err = f(info)
			if err != nil {
				if try < maxTry-1 {
					time.Sleep(time.Duration(try+1) * time.Second) // 退避
				}
				continue
			}
			break
		}
		return r, err
	}
}

// 获取对应业务的在线信息、视频开播信息: 返回业务id到在线信息列表的映射 以及 在线频道集合
// m: businessID => []onLineChannelInfo
// videoChannel: 交友、约战、宝贝三个业务的在线视频频道
func getOnlineChannelInfo() (m map[int][]*onlineChannelInfo, videoChannel map[Channel]bool, err error) {
	m = make(map[int][]*onlineChannelInfo)
	addOnlineChannelInfo := func(business int, info []*onlineChannelInfo) {
		if info != nil {
			m[business] = info
		}
		yylog.Info("add online channel info", zap.Int("business", business), zap.Int("info length", len(info)))
	}

	info, err := onlineChannelInfoRetry(getJYOnlineChannelInfo)()
	if err != nil {
		yylog.ErrorWithBam("cannot get jy online channel info", zap.Error(err))
	}
	addOnlineChannelInfo(businessJY, info)

	appendVideoChannel := func(m map[Channel]bool) {
		for k, v := range m {
			if videoChannel == nil {
				videoChannel = make(map[Channel]bool)
			}
			videoChannel[k] = v
		}
	}
	vc, err := getVideoChannelRetry(getJYVideoChannel)(info)
	if err != nil {
		yylog.ErrorWithBam("cannot get jy video channel", zap.Error(err))
	}
	appendVideoChannel(vc)

	info, err = onlineChannelInfoRetry(getBabyOnlineChannelInfo)()
	if err != nil {
		yylog.ErrorWithBam("cannot get baby online channel info", zap.Error(err))
	}
	addOnlineChannelInfo(businessBaby, info)

	vc, err = getVideoChannelRetry(getBabyVideoChannel)(info)
	if err != nil {
		yylog.ErrorWithBam("cannot get baby video channel", zap.Error(err))
	}
	appendVideoChannel(vc)

	zwOnlineChannelInfo, err := getZWOnlineChannelInfo()
	if err != nil {
		yylog.ErrorWithBam("cannot get zw online channel info", zap.Error(err))
	}
	for k, v := range zwOnlineChannelInfo {
		addOnlineChannelInfo(k, v)
	}

	info, err = onlineChannelInfoRetry(getConfigBusinessBasicOnlineChannelInfo)()
	if err != nil {
		yylog.ErrorWithBam("cannot get basic online channel info", zap.Error(err))
	}
	addOnlineChannelInfo(businessBasic, append([]*onlineChannelInfo{}, info...))

	info, err = onlineChannelInfoRetry(getConfigBusinessPWOnlineChannelInfo)()
	if err != nil {
		yylog.ErrorWithBam("cannot get pw online channel info", zap.Error(err))
	}
	addOnlineChannelInfo(businessPW, append([]*onlineChannelInfo{}, info...))
	return m, videoChannel, nil
}

// 获取开黑车队在线列表
func getWaitingGameTeamInfo() (list []waitingTeamInfo, err error) {
	start := time.Now()
	const maxTry = 4
	for try := 0; try < maxTry; try++ {
		list, err = getWaitingTeamData()
		if err != nil {
			yylog.Warn("query fail", zap.Int("times", try), zap.Error(err))
			if try < maxTry-1 {
				time.Sleep(time.Duration(try+1) * time.Second) // 退避
			}
			continue
		}
		break
	}
	sort.Slice(list, func(i, j int) bool { // 按创建时间升序排序
		return list[i].StartTime < list[j].StartTime
	})
	yylog.Info("query waitingTeam list", zap.NamedError("lastErr", err),
		zap.Int("n_list", len(list)),
		zap.Duration("cost", time.Since(start)))
	return
}

// 对应业务的推荐信息
func getOnlineChannelInfoConcurrent() (map[int][]*onlineChannelInfo, map[Channel]bool, error) {
	var onlineLock sync.Mutex
	var videoChannelLock sync.Mutex
	m := make(map[int][]*onlineChannelInfo)
	videoChannel := make(map[Channel]bool)

	onlineChannelInfo := func(k int, v []*onlineChannelInfo, isAppend bool) {
		onlineLock.Lock()
		defer onlineLock.Unlock()
		if isAppend {
			m[k] = append(m[k], v...)
		} else {
			m[k] = v
		}
	}

	appendVideoChannel := func(m map[Channel]bool) {
		videoChannelLock.Lock()
		defer videoChannelLock.Unlock()
		for k, v := range m {
			videoChannel[k] = v
		}
	}

	err := util.BatchWork(
		func() error {
			info, err := onlineChannelInfoRetry(getJYOnlineChannelInfo)()
			if err != nil {
				return err
			}
			onlineChannelInfo(businessJY, info, false)
			yylog.Info("jy online info", zap.Int("len(info)", len(info)), zap.Int("len(m)", len(m)))

			vc, err := getVideoChannelRetry(getJYVideoChannel)(info)
			if err != nil {
				return err
			}
			appendVideoChannel(vc)
			return err
		},
		func() error {
			info, err := onlineChannelInfoRetry(getBabyOnlineChannelInfo)()
			if err != nil {
				return err
			}
			onlineChannelInfo(businessBaby, info, false)
			yylog.Info("baby online info", zap.Int("len(info)", len(info)), zap.Int("len(m)", len(m)))

			vc, err := getVideoChannelRetry(getBabyVideoChannel)(info)
			if err != nil {
				return err
			}
			appendVideoChannel(vc)
			return err
		},
		func() error {
			pgOnlineChannelInfo, err := getZWOnlineChannelInfo()
			if err != nil {
				return nil
			}
			for k, v := range pgOnlineChannelInfo {
				onlineChannelInfo(k, v, false)
			}
			return err
		},
		func() error {
			info, err := onlineChannelInfoRetry(getConfigBusinessBasicOnlineChannelInfo)()
			if err != nil {
				return err
			}
			onlineChannelInfo(businessBasic, info, true)
			yylog.Info("basic online info", zap.Int("len(info)", len(info)), zap.Int("len(m)", len(m)))
			return err
		},
		func() error {
			info, err := onlineChannelInfoRetry(getConfigBusinessPWOnlineChannelInfo)()
			if err != nil {
				return err
			}
			onlineChannelInfo(businessPW, info, true)
			yylog.Info("pw online info", zap.Int("len(info)", len(info)), zap.Int("len(m)", len(m)))
			return err
		},
	)
	if err != nil {
		yylog.Error("get online info err", zap.Error(err))
		return m, videoChannel, err
	}

	return m, videoChannel, nil
}

// 查询追玩推荐信息库配置数据?
func getZhuiyaRecommendConfigList(ids []string, tip int32) ([]BossPostData, error) {
	const n = 200
	var ret []BossPostData
	for len(ids) > n {
		data, err := GetZhuiyaRecommendConfigList(ids[:n], tip)
		if err != nil {
			return ret, err
		}
		ret = append(ret, data...)
		ids = ids[n:]
	}
	if len(ids) > 0 {
		data, err := GetZhuiyaRecommendConfigList(ids, tip)
		if err != nil {
			return ret, err
		}
		ret = append(ret, data...)
	}
	return ret, nil
}

func priorityPictureOrTitle(s ...string) string {
	for _, ss := range s {
		if len(ss) > 0 {
			return ss
		}
	}
	return ""
}

// 从追玩后台查询频道推荐信息, 返回推荐封面,推荐标题等数据
func getFeatureInfo(ids,
	channelIDs []string,
	remarksMap map[Channel]string) (
	map[string]string,
	map[string]string,
	map[string]string,
	map[string]string,
	error) {
	cover := make(map[string]string)
	title := make(map[string]string)
	label := make(map[string]string)
	contentLabel := make(map[string]string)
	auditPassedPictureOrTitle := func(s string, status int32) string {
		if StateAuditPassed == status {
			return s
		}
		return ""
	}

	for _, tip := range []int32{mgodao.RecommendTipUID, mgodao.RecommendTipSID} {
		var data []BossPostData
		var err error
		if tip == mgodao.RecommendTipUID {
			data, err = getZhuiyaRecommendConfigList(ids, tip)
		} else {
			data, err = getZhuiyaRecommendConfigList(channelIDs, tip)
		}
		if err != nil {
			return cover, title, label, contentLabel, err
		}
		for _, d := range data {
			pc := auditPassedPictureOrTitle(d.ConfigPicture, d.ConfigPictureStatus)
			pu := auditPassedPictureOrTitle(d.UploadPicture, d.UploadPictureStatus)
			cover[d.ID] = priorityPictureOrTitle(pc, pu)

			c := Channel{Sid: d.SID, Ssid: d.SSID}
			remarks, _ := remarksMap[c]
			tc := auditPassedPictureOrTitle(d.ConfigTitle, d.ConfigTitleStatus)
			tu := auditPassedPictureOrTitle(d.UploadTitle, d.UploadTitleStatus)
			title[d.ID] = priorityPictureOrTitle(tc, tu, remarks)

			label[d.ID] = d.FeaturePictureURL
			contentLabel[d.ID] = d.ContentLabel
			yylog.Debug("get feature info", zap.Any("d", d))
		}
	}
	yylog.Debug("get feature info", zap.Any("cover", cover), zap.Any("title", title), zap.Any("label", label))
	return cover, title, label, contentLabel, nil
}

// 获取频道的人气值
func getRoomPopularityValue(id int64, terminal int, channels []Channel) (map[Channel]int64, error) {
	if len(channels) == 0 {
		return nil, nil
	}
	req := fts_ch_popular.NewGetRoomPopularityValueReq()
	for _, c := range channels {
		req.Channels = append(req.Channels, &fts_ch_popular.Channel{
			Sid:      c.Sid,
			Ssid:     c.Ssid,
			ID:       id,
			Terminal: int64(terminal),
		})
	}
	now := time.Now()
	ret, err := chpopulardao.GetRoomPopularityValue(req)
	if err != nil || ret == nil || ret.Status != 0 {
		return nil, err
	}
	yylog.Info("get room popularity value", zap.Int64("id", id), zap.Int("channelSize", len(channels)), zap.Any("timeElapsed", time.Since(now)))
	m := make(map[Channel]int64, len(ret.Values))
	for _, r := range ret.Values {
		c := Channel{Sid: r.Sid, Ssid: r.Ssid}
		m[c] = r.GetValue()
		yylog.Debug("get room popularity value", zap.Int64("id", id), zap.Any("c", c), zap.Int64("psu", r.GetValue()))
	}
	return m, nil
}

func getAttributes(terminal int, gameType int, business int, icon string) string {
	if business != businessJY {
		return icon
	}
	if !isAPPTerminal(terminal) {
		m := map[int]string{
			constinfo.KOrdinary:     "http://makefriends.bs2dl.yy.com/1625474821_80a26185181ecf10287d8fedd492e52d.png",
			constinfo.KChannelFight: "http://makefriends.bs2dl.yy.com/1625474866_f05c9e9da02ca0fc8c4c939274e4e430.png",
			constinfo.KTeamFight:    "http://makefriends.bs2dl.yy.com/1625474847_cb4dd874d5069e3a360353b182c4af1a.png",
		}
		return m[gameType]
	}
	m := map[int]string{
		constinfo.KOrdinary:     "http://makefriends.bs2dl.yy.com/1641957265_ebd6673e4243b52f429f6d1c453682ef.png",
		constinfo.KChannelFight: "http://makefriends.bs2dl.yy.com/1641957243_004c5b96519061af48cd359ba2da0785.png",
		constinfo.KTeamFight:    "http://makefriends.bs2dl.yy.com/1641957210_0828523fd1c7dfa7fda4173fa36e0e0f.png",
	}
	return m[gameType]
}

func batchSubChannelPasswordMapInfo(channels []Channel) map[Channel]bool {
	var cs [][]string
	for _, c := range channels {
		cs = append(cs, []string{strconv.FormatInt(c.Sid, 10), strconv.FormatInt(c.Ssid, 10)})
	}
	m := webdbdao.BatchSubChannelPasswordMapInfo(cs)
	r := make(map[Channel]bool, len(m))
	for k, v := range m {
		s := strings.Split(k, ":")
		if len(s) != 2 {
			continue
		}
		sid, _ := strconv.ParseInt(s[0], 10, 64)
		ssid, _ := strconv.ParseInt(s[1], 10, 64)
		c := Channel{Sid: sid, Ssid: ssid}
		r[c] = v
	}
	yylog.Debug("channel password", zap.Any("r", r))
	return r
}

func batchGetChannelTemplateID(channels []Channel) (map[string]int64, error) {
	var channelStr []string
	for _, c := range channels {
		channelStr = append(channelStr, toChannelStr(c.Sid, c.Ssid))
	}
	return webdbdao.BatchGetChannelTemplateID(channelStr)
}

// 批量查询追玩频道信息
func batchGetGameChannelInfo(channels []Channel) (map[*zhuiwan_rec_data.SidInfo]*zhuiwan_rec_data.ChannelInfo, error) {
	if len(channels) == 0 {
		return nil, nil
	}
	var sidInfo []*zhuiwan_rec_data.SidInfo
	for _, c := range channels {
		sidInfo = append(sidInfo, &zhuiwan_rec_data.SidInfo{
			Sid:  c.Sid,
			Ssid: c.Ssid,
		})
	}
	r, err := zhuiwanrecdatadao.BatchQueryData(sidInfo)
	if err != nil {
		return nil, err
	}
	if r == nil {
		return nil, nil
	}
	if r.Code != 0 {
		return nil, fmt.Errorf("r=%+v, code=%d, msg=%s", r, r.Code, r.Msg)
	}
	return r.DataMap, nil
}

// 批量查询频道的副标题等其他信息, 查询remarkMap, subTitleMap
func batchGetGameChannelRemarksAndSubTitle(channels []Channel) (map[Channel]string, map[Channel]string, error) {
	r, err := batchGetGameChannelInfo(channels)
	if err != nil {
		return nil, nil, nil
	}
	var remarksMap map[Channel]string
	var subTitleMap map[Channel]string
	for _, v := range r {
		if v == nil {
			continue
		}
		c := Channel{Sid: v.Sid, Ssid: v.Ssid}
		if remarksMap == nil {
			remarksMap = make(map[Channel]string)
		}
		if subTitleMap == nil {
			subTitleMap = make(map[Channel]string)
		}
		remarksMap[c] = v.Remarks
		subTitleMap[c] = v.SubTitle
		yylog.Debug("channel remarks and sub title", zap.Any("c", c), zap.String("remarks", v.Remarks), zap.String("subTitle", v.SubTitle))
	}
	yylog.Info("channel remarks and sub title", zap.Int("len(channels)", len(channels)),
		zap.Int("len(remarksMap)", len(remarksMap)), zap.Int("len(subTitleMap)", len(subTitleMap)))
	return remarksMap, subTitleMap, nil
}

func channelFirst(m map[string]string, item *viewItem) string {
	if m == nil {
		return ""
	}
	id := fmt.Sprintf("%d_%d_%d", item.Sid, item.Ssid, item.Business)
	if len(m[id]) > 0 {
		return m[id]
	}
	id = fmt.Sprintf("%d_%d", item.UID, item.Business)
	return m[id]
}

func getCoverOrTitle(m map[string]string, item *viewItem) string {
	if m == nil {
		return ""
	}
	// 多人视频：运营ssid封标>厅管ssid封标>运营uid封标>主持uid封标
	if item.Mode == 0 || isMultiVideoGameType(item.GameType) {
		return channelFirst(m, item)
	}
	var id string
	if isRecommendedByCompere(item.Mode) {
		id = fmt.Sprintf("%d_%d", item.UID, item.Business)
	} else {
		id = fmt.Sprintf("%d_%d_%d", item.Sid, item.Ssid, item.Business)
	}
	return m[id]
}

// 批量查询技能卡频道信息
func batchGetSkillChannelInfo(channels []Channel) (map[Channel]*zhuiwan_channel.ChannelInfo, error) {
	if len(channels) == 0 {
		return nil, nil
	}
	var req zhuiwan_channel.BatchGetSkillChannelInfoReq
	for _, c := range channels {
		req.ChannelList = append(req.ChannelList, &zhuiwan_channel.ChannelReq{
			TopSid: c.Sid,
			SubSid: c.Ssid,
		})
	}
	req.Timestamp = time.Now().UnixNano()
	ret, err := zhuiwandao.BatchGetSkillChannelInfoV2(&req)
	if err != nil {
		return nil, err
	}
	m := make(map[Channel]*zhuiwan_channel.ChannelInfo)
	for _, r := range ret {
		if r == nil {
			continue
		}
		c := Channel{Sid: r.TopSid, Ssid: r.SubSid}
		m[c] = r
		yylog.Debug("skill channel info", zap.Any("c", c), zap.Any("r", r))
	}
	return m, nil
}

// 批量查询流信息
func batchGetStreamInfo(ssidList []uint64) (map[uint64]string, error) {
	if len(ssidList) == 0 {
		return nil, nil
	}
	m := make(map[uint64]string)
	const n = 400
	for len(ssidList) > 0 {
		var r *proto_stream_manager.SaResponseBatchStreamInfo
		var err error
		if len(ssidList) < n {
			r, err = liveinfodao.BatchGetStreamInfo(ssidList)
		} else {
			r, err = liveinfodao.BatchGetStreamInfo(ssidList[:n])
		}
		if err != nil || r == nil || r.Rescode != proto_stream_manager.CommonResCode_COM_RES_OK {
			yylog.Error("cannot batch get stream info", zap.Any("r", r), zap.Error(err))
		}
		if r != nil {
			for _, ssid := range ssidList {
				stream, _ := r.InfoMap[ssid]
				yylog.Debug("", zap.Uint64("ssid", ssid), zap.Any("stream", stream))
				if stream == nil {
					continue
				}
				originStream := &proto_stream_manager.ChannelStreamInfo{
					Version: stream.Version,
				}
				for _, s := range stream.Streams {
					// 原画
					if s.Mix == 1 || s.Mix == 2 {
						originStream.Streams = append(originStream.Streams, s)
					}
				}
				b, err := proto.Marshal(originStream)
				if err != nil {
					yylog.Error("cannot marshal pb", zap.Any("stream", stream), zap.Error(err))
					continue
				}
				if len(b) > 0 {
					m[ssid] = base64.StdEncoding.EncodeToString(b)
				}
			}
			yylog.Info("batch get stream info", zap.Int("len(ssidList)", len(ssidList)), zap.Int("len(m)", len(m)))
		}
		if len(ssidList) < n {
			ssidList = ssidList[:0]
		} else {
			ssidList = ssidList[n:]
		}
	}
	return m, nil
}

// 返回首个不为空的字符串
//
//	param reverse 反序，如果是true则从最后一个开始判断，首个不为空字符串的则返回
func getFirstNotEmptyString(sList ...string) string {
	if len(sList) == 0 {
		return ""
	}
	for _, s := range sList {
		if len(s) != 0 {
			return s
		}
	}
	return ""
}

// 适配推荐缩略图
func adapterRecommendCover(terminal int, cover string) string {
	if len(cover) == 0 {
		return cover
	}
	if isAPPTerminal(terminal) && (strings.Contains(cover, "bs2dl.yy.com") || strings.Contains(cover, "bs2cdn.yy.com")) && !strings.Contains(cover, ".gif") {
		cover = getImageURL(terminal, cover)
		cover += "?x-bce-process=image/resize,m_fill,w_340,h_340"
	}
	return cover
}

func toPlayTagKey(roomTypeID, gameType int) string {
	return fmt.Sprintf("%d:%d", roomTypeID, gameType)
}

func getRoomPlayTag(style int) (map[string]string, error) {
	s, err := redisdao.GetRoomPlayTag()
	if err != nil {
		return nil, err
	}
	var tags []ZwRoomPlayTags
	if err := json.Unmarshal([]byte(s), &tags); err != nil {
		return nil, err
	}
	playTagMap := make(map[string]string)
	for _, tag := range tags {
		key := toPlayTagKey(tag.RoomTypeID, tag.Type)
		switch style {
		case 0: // 样式1
			playTagMap[key] = tag.Icon1
		case 1: // 样式2
			playTagMap[key] = tag.Icon2
		case 2: // 样式3
			playTagMap[key] = tag.Icon1
		case 3: // 样式4
			playTagMap[key] = tag.Icon3
		case 4: // 样式5
			playTagMap[key] = tag.Icon3
		}
	}
	yylog.Debug("get room play tag", zap.Any("tag", playTagMap))
	return playTagMap, nil
}

func checkSensitiveGame(tabConfig *mgodao.RecommendTabConfig,
	batchCheckSensitiveGameReq *fts_recommend_filter.BatchCheckRecommendReq) func(int64, int64, int64) (bool, string) {
	var err error
	checkRecommendResp, err := recommendfilterdao.BatchCheckRecommend(batchCheckSensitiveGameReq)
	if err != nil {
		yylog.Warn("cannot batch check recommend", zap.Int64("id", tabConfig.ID), zap.Error(err))
	}
	yylog.Debug("",
		zap.Reflect("batchCheckSensitiveGameReq", batchCheckSensitiveGameReq),
		zap.Reflect("checkRecommendResp", checkRecommendResp))

	recallRecommendRoomRsp, err := skillcardroomafkdao.RecallRecommendRoom(context.Background())
	if err != nil {
		yylog.Error("RecallRecommendRoom err", zap.Error(err))
	}
	recallRecommendRoomMap := make(map[Channel]string)
	if recallRecommendRoomRsp != nil {
		for _, r := range recallRecommendRoomRsp.Data {
			c := Channel{Sid: r.Sid, Ssid: r.Ssid}
			recallRecommendRoomMap[c] = r.ConditionText
		}
	}
	return func(uid, sid, ssid int64) (bool, string) {
		if checkRecommendResp == nil || checkRecommendResp.ResMap == nil {
			return false, ""
		}
		res := checkRecommendResp.ResMap[uid]
		yylog.Debug("batch check recommend", zap.Int64("id", tabConfig.ID), fts.UID(uid), fts.SSID(sid),
			fts.SID(ssid), zap.Any("res", res))
		if res != nil && res.IsHit {
			return res.IsHit, res.Reason
		}
		c := Channel{Sid: sid, Ssid: ssid}
		if conditionText, ok := recallRecommendRoomMap[c]; ok {
			return true, conditionText
		}
		return false, ""
	}
}

func blackList(tabConfig *mgodao.RecommendTabConfig) func(int64, int64, int64) bool {
	var sidBlackList, uidBlackList map[int64]bool
	var channelBlackList map[string]bool
	var err error
	if tabConfig.CheckBlackList {
		sidBlackList, channelBlackList, uidBlackList, err = mgodao.GetRecommendBlackListMap()
		if err != nil {
			yylog.Warn("cannot get recommend black list", zap.Int64("id", tabConfig.ID), zap.Error(err))
		}
	}
	yylog.Info("black list", zap.Int64("id", tabConfig.ID), zap.Int("len(sidBlackList)", len(sidBlackList)),
		zap.Int("len(channelBlackList)", len(channelBlackList)), zap.Int("len(uidBlackList)", len(uidBlackList)))

	// 判断频道或uid是否在黑名单中
	return func(sid, ssid, uid int64) bool {
		if sidBlackList != nil && sidBlackList[sid] {
			return true
		}
		c := toChannelStr(sid, ssid)
		if channelBlackList != nil && channelBlackList[c] {
			return true
		}
		if uidBlackList != nil && uidBlackList[uid] {
			return true
		}
		return false
	}
}

// 运营配置：整个位置处于未推荐状态
func positionIsRecommend(id string) bool {
	now := time.Now().Unix()
	rmc := getRecommendManageConfig(id)
	if rmc != nil && rmc.IsAuditConfigMode() {
		if !rmc.IsRecommendStatus() {
			return false
		}
		return !rmc.RecommendTimeExpired(now)
	}
	return true
}

func showLabel(id string, expired bool) bool {
	rmc := getRecommendManageConfig(id)
	if rmc != nil {
		if rmc.IsAutoRecommendConfigMode() && !rmc.IsShowLabelStatus() {
			return false
		} else if rmc.IsAuditConfigMode() && expired {
			return false
		}
	}
	return true
}

func showAttributes(id string, showAttributesStatus bool) bool {
	rmc := getRecommendManageConfig(id)
	if rmc != nil && !rmc.IsShowAttributesStatus() {
		return false
	}
	return showAttributesStatus
}

func filterViewItem(tabConfig *mgodao.RecommendTabConfig,
	batchCheckSensitiveGameReq *fts_recommend_filter.BatchCheckRecommendReq,
	coverMap map[string]string,
	titleMap map[string]string,
	voiceRoomDataMap map[int64]recommenddatabasedao.VoiceRoomRecommendInfo,
	baiduVoiceRoomDataMap map[int64]recommenddatabasedao.BaiduVoiceRoomRecommendIInfo) func(item *viewItem) (bool, string) {
	isSensitiveGame := checkSensitiveGame(tabConfig, batchCheckSensitiveGameReq)

	return func(item *viewItem) (bool, string) {
		if ok, reason := isSensitiveGame(item.UID, item.Sid, item.Ssid); ok {
			yylog.Warn("can't be recommended: sensitive game", zap.Int64("id", tabConfig.ID), zap.Any("item", item), zap.String("reason", reason))
			return true, reason
		}
		if !positionIsRecommend(item.RecommendManageID) {
			yylog.Warn("can't be recommended: position", zap.Int64("id", tabConfig.ID), zap.Any("item", item))
			return true, "位置未推荐"
		}
		if item.RoomNo == 0 {
			item.Cover = getCoverOrTitle(coverMap, item)
		} else {
			item.Cover = priorityPictureOrTitle(voiceRoomDataMap[item.RoomNo].ConfigPic, voiceRoomDataMap[item.RoomNo].UploadPic)
		}

		// 没有推荐图不推荐, 配置了不允许缺失推荐图
		if len(item.Cover) == 0 && tabConfig.AllowMissCover == 0 {
			yylog.Warn("can't be recommended: not cover", zap.Int64("id", tabConfig.ID), zap.Any("item", item))
			return true, "没有推荐图"
		}
		if item.RoomNo == 0 {
			item.Title = getCoverOrTitle(titleMap, item)
		} else {
			item.Title = priorityPictureOrTitle(voiceRoomDataMap[item.RoomNo].ConfigTitle, voiceRoomDataMap[item.RoomNo].UploadTitle)
		}

		if tabConfig.AllowMissCover == 0 && item.Business != businessBaby && len(item.Title) == 0 {
			yylog.Warn("can't be recommended: not title", zap.Int64("id", tabConfig.ID), zap.Any("item", item))
			return true, "没有推荐标题"
		}
		if item.Psu < tabConfig.PopularityValueMin {
			yylog.Warn("can't be recommended: popularity value min not reach", zap.Int64("id", tabConfig.ID), zap.Any("item", item))
			return true, fmt.Sprintf("人气值未达到最小要求，当前：%v < %v", item.Psu, tabConfig.PopularityValueMin)
		}
		if fts_supplement.RecommendType_kVoiceRoomMoreExciting == fts_supplement.RecommendType(tabConfig.SupplementRecommendType) {
			item.BdCover = priorityPictureOrTitle(baiduVoiceRoomDataMap[item.RoomNo].ConfigPic1x1, baiduVoiceRoomDataMap[item.RoomNo].UploadPic1x1)
			if len(item.BdCover) == 0 {
				yylog.Warn("can't be recommended: not baidu cover", zap.Int64("id", tabConfig.ID), zap.Any("item", item))
				return true, "没有百度语音房联运推荐图"
			}

			item.BdTitle = priorityPictureOrTitle(baiduVoiceRoomDataMap[item.RoomNo].ConfigTitle, baiduVoiceRoomDataMap[item.RoomNo].UploadTitle)
			if len(item.BdTitle) == 0 {
				yylog.Warn("can't be recommended: not baidu title", zap.Int64("id", tabConfig.ID), zap.Any("item", item))
				return true, "没有百度语音房联运推荐标题"
			}
		}
		return false, ""
	}
}

func isRecommendedByCompere(mode int) bool {
	item := mgodao.RecommendItem{Mode: mode}
	return item.IsRecommendCompere()
}

func isMultiVideoGameType(gameType int) bool {
	return gameType == constinfo.KVideoDating ||
		gameType == constinfo.KVideoPk ||
		gameType == constinfo.KVideoFightMatch
}

func isHdgameGameType(gameType int) bool {
	return gameType == constinfo.KVideoDatingNew ||
		gameType == constinfo.KVideoPkNew ||
		gameType == constinfo.KVideoFightMatchNew
}

func appendIdList() func(ids []string, channelIds []string, item *viewItem) ([]string, []string) {
	idSet := make(map[string]bool)
	return func(ids []string, channelIds []string, item *viewItem) ([]string, []string) {
		// 多人视频：运营ssid封标>运营uid封标>厅管ssid封标>主持uid封标
		if item.Mode == 0 || isMultiVideoGameType(item.GameType) {
			id := fmt.Sprintf("%d_%d", item.UID, item.Business)
			if !idSet[id] {
				ids = append(ids, id)
				idSet[id] = true
			}
			id = fmt.Sprintf("%d_%d_%d", item.Sid, item.Ssid, item.Business)
			if !idSet[id] {
				channelIds = append(channelIds, id)
				idSet[id] = true
			}
			return ids, channelIds
		}
		if isRecommendedByCompere(item.Mode) {
			id := fmt.Sprintf("%d_%d", item.UID, item.Business)
			if !idSet[id] {
				ids = append(ids, id)
				idSet[id] = true
			}
		} else {
			id := fmt.Sprintf("%d_%d_%d", item.Sid, item.Ssid, item.Business)
			if !idSet[id] {
				channelIds = append(channelIds, id)
				idSet[id] = true
			}
		}
		return ids, channelIds
	}
}

func liveStreamInfo(ssidList []uint64) (map[uint64]string, map[string]string) {
	stream, err := batchGetStreamInfo(ssidList)
	if err != nil {
		yylog.Warn("cannot batch get stream info", zap.Error(err))
	}
	mixStream, err := redisdao.GetAllLiveSpeed()
	if err != nil {
		yylog.Warn("cannot get live speed", zap.Error(err))
	}
	return stream, mixStream
}

// 补充推荐信息中的其他信息(推荐封面、人气值、主持昵称等), 并过滤不该显示的频道(在黑名单、频道假名、无推荐图等)
func getViewItem(materials *recommendMaterials, vi []*viewItem) ([]*viewItem, []*notRecommendItem) {
	var ids []string        // uid_business
	var channelIds []string // sid_ssid_business
	var uids []int64
	var channels []Channel
	var gameChannels []Channel // 追玩业务的频道
	channelSet := make(map[Channel]bool)
	var ssidList []uint64
	var roomNoList []int64
	id, platform, tabConfig := materials.conf.ID, materials.platform, materials.conf
	batchCheckSensitiveGameReq := fts_recommend_filter.BatchCheckRecommendReq{
		Navigation: fts_recommend_filter.NavigationType_ZhuiWan,
	}
	idList := appendIdList()
	for _, v := range vi {
		c := Channel{Sid: v.Sid, Ssid: v.Ssid}
		if channelSet[c] {
			continue
		}
		channelSet[c] = true
		uids = append(uids, v.UID)
		channels = append(channels, c)
		if isZhuiWanChannel(v.Business) {
			gameChannels = append(gameChannels, c)
		}
		if v.RoomNo > 0 {
			roomNoList = append(roomNoList, v.RoomNo)
		}
		ssidList = append(ssidList, uint64(v.Ssid))

		recommendType := fts_recommend_filter.RecommendType_ByUID
		item := mgodao.RecommendItem{Mode: v.Mode}
		if item.IsRecommendChannel() {
			recommendType = fts_recommend_filter.RecommendType_ByChannel
		}
		batchCheckSensitiveGameReq.RecommendList = append(batchCheckSensitiveGameReq.RecommendList,
			&fts_recommend_filter.RecommendItem{
				UID:           v.UID,
				Sid:           v.Sid,
				Ssid:          v.Ssid,
				RecommendType: recommendType,
				EnterType:     v.EnterType,
				ZoneID:        v.ConfZoneID,
			})
		ids, channelIds = idList(ids, channelIds, v)
	}

	var err error
	var remarksMap, subTitleMap map[Channel]string
	remarksMap, subTitleMap, err = batchGetGameChannelRemarksAndSubTitle(gameChannels)
	if err != nil {
		yylog.Warn("cannot get remarks and sub title", zap.Int64("id", id), zap.Int("platform", platform), zap.Int("len(vi)", len(vi)), zap.Error(err))
	}

	var stream map[uint64]string
	var mixStream map[string]string
	if platform != platformPC {
		stream, mixStream = liveStreamInfo(ssidList)
	}

	coverMap, titleMap, labelMap, contentLabelMap, err := getFeatureInfo(ids, channelIds, remarksMap)
	if err != nil {
		yylog.Warn("cannot get cover and title", zap.Int64("id", id), zap.Int("platform", platform),
			zap.Int("len(vi)", len(vi)), zap.Error(err))
	}

	zwChannelInfoMap, err := batchGetSkillChannelInfo(gameChannels)
	if err != nil {
		yylog.Warn("cannot batch get skill channel info", zap.Int64("id", id),
			zap.Int("platform", platform), zap.Int("len(vi)", len(vi)), zap.Error(err))
	}

	popularityMap, err := getRoomPopularityValue(id, tabConfig.Terminal, channels)
	if err != nil {
		yylog.Warn("cannot get room popularity value", zap.Int64("id", id), zap.Int("platform", platform),
			zap.Int("len(channels)", len(channels)), zap.Int("len(popularityMap)", len(popularityMap)), zap.Error(err))
	}

	const defaultAvatar = "https://s1.yy.com/guild/header/10001.jpg"
	webdbMap, err := webdbdao.BatchGetYYNickAndLogo(uids)
	if err != nil {
		yylog.Warn("cannot batch get yy nick and avatar", zap.Int64("id", id), zap.Int("platform", platform),
			zap.Int("len(uids)", len(uids)), zap.Int("len(webdbMap)", len(webdbMap)), zap.Error(err))
	}

	sexMap, err := webdbdao.BatchGetSex(uids)
	if err != nil {
		yylog.Warn("cannot batch get yy nick and avatar", zap.Int64("id", id), zap.Int("platform", platform),
			zap.Int("len(uids)", len(uids)), zap.Int("len(sexMap)", len(sexMap)), zap.Error(err))
	}

	templateIDMap, _ := batchGetChannelTemplateID(channels)

	playTagMap, _ := getRoomPlayTag(tabConfig.Style)
	voiceRoomDataMap, _ := recommenddatabasedao.BatchVoiceRoomRecommendInfo(roomNoList)
	baiduVoiceRoomDataMap, _ := recommenddatabasedao.GetBaiduVoiceRoomRecommendInfo(roomNoList)

	isFilterViewItem := filterViewItem(tabConfig,
		&batchCheckSensitiveGameReq,
		coverMap,
		titleMap,
		voiceRoomDataMap,
		baiduVoiceRoomDataMap)

	var ret []*viewItem
	var nri []*notRecommendItem
	for _, item := range vi {
		if ok, reason := isFilterViewItem(item); ok {
			nri = append(nri, &notRecommendItem{
				UID:    item.UID,
				Sid:    item.Sid,
				Ssid:   item.Ssid,
				ZoneID: item.ZoneID - item.Position,
				Reason: reason,
			})
			yylog.Warn("filter view item", zap.String("reason", reason), zap.Int64("id", tabConfig.ID),
				zap.Int("platform", platform), zap.Any("item", item))
			continue
		}
		// 缩略图适配
		item.Cover = adapterRecommendCover(tabConfig.Terminal, item.Cover)

		// title、label优先使用厅配置
		// label、attributes显示统一由推荐管理配置
		if showLabel(item.RecommendManageID, item.LabelExpired) {
			if item.RoomNo == 0 {
				item.Label = channelFirst(labelMap, item)
			} else {
				item.Label = voiceRoomDataMap[item.RoomNo].ConfigLabel
			}
			item.Label = getImageURL(tabConfig.Terminal, item.Label)
		}
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		if zwChannelInfo, ok := zwChannelInfoMap[c]; ok {
			item.RoomTag = zwChannelInfo.RoomTag
			item.Lock = zwChannelInfo.IsLocked == 1
			item.Background = zwChannelInfo.Background
			item.BackgroundNew = zwChannelInfo.BackgroundNew
			if zwChannelInfo.RoomTypeId > 0 {
				item.RoomType = int(zwChannelInfo.RoomTypeId)
			}
			if zwChannelInfo.RoomClass > 0 {
				item.RoomClass = int(zwChannelInfo.RoomClass)
			}
		}
		if showAttributes(item.RecommendManageID, item.ShowAttributesStatus) {
			item.Attributes = getAttributes(tabConfig.Terminal, item.GameType, item.Business, item.Icon)
			item.Attributes = getImageURL(tabConfig.Terminal, item.Attributes) // yo1.3开始废弃，改用attributesIcon字段
			// 没有设置内容标签，取玩法标签
			attributesIcon := channelFirst(contentLabelMap, item)
			if len(attributesIcon) == 0 {
				attributesIcon, _ = playTagMap[toPlayTagKey(item.RoomType, item.GameType)]
			}
			item.AttributesIcon = getImageURL(tabConfig.Terminal, attributesIcon)
		} else {
			item.BigIcon = "" // yo1.3开始废弃，改用attributesIcon字段
		}
		item.Psu, _ = popularityMap[c]

		item.Sex, _ = sexMap[item.UID]
		personInfo, _ := webdbMap[item.UID]
		if personInfo.HdLogo != defaultAvatar {
			item.Avatar = personInfo.HdLogo
		}
		if len(item.Nick) == 0 {
			item.Nick = personInfo.Nick
		}
		if len(item.Title) == 0 && item.Business == businessBaby {
			item.Title = personInfo.Nick
		}
		if item.SubTitle, _ = subTitleMap[c]; len(item.SubTitle) == 0 { // 追玩3.7需求新增: 副标题为空时用昵称代替
			item.SubTitle = item.Nick
		}
		if item.TemplateID == 0 {
			item.TemplateID, _ = templateIDMap[toChannelStr(item.Sid, item.Ssid)]
		}
		item.LiveInfo, _ = stream[uint64(item.Ssid)]
		item.MixStream, _ = mixStream[toChannelStr(item.Sid, item.Ssid)]
		for i := range item.OnlineInfo {
			if len(item.OnlineInfo[i].Avatar) == 0 {
				item.OnlineInfo[i].Avatar = getDefaultAvatar(tabConfig.Terminal)
			}
		}
		yylog.Info("recommend item", zap.Int64("id", id), zap.Int("platform", platform), zap.Int64("zoneId", item.ZoneID),
			fts.UID(item.UID), fts.SSID(item.Sid), fts.SID(item.Ssid), zap.Any("item", item))
		ret = append(ret, item)
	}
	return ret, nri
}

// 从补量系统获取推荐列表
// 获取到推荐列表后，填充流信息，过统一规则（如黑名单）
// 推荐接口文档： http://jywiki.yy.com/docs/other_apis/other_apis-1cu0aajnm4e7h#b9zpap
func getSupplementRecommendViewItems(materials *recommendMaterials) (list []*viewItem, unrecommendList []*notRecommendItem, err error) {
	conf, platform, onlineInfo := materials.conf, materials.platform, materials.oi()
	var supplementFetchCount int
	defer func() {
		if err != nil {
			alert.MonitorAlert(&err)
			yylog.Error("compute supplement recommend error", zap.Int64("id", conf.ID), zap.Int("recommendType", conf.SupplementRecommendType),
				zap.Int("platform", platform), zap.Error(err))
			return
		}
		yylog.Debug("compute supplement recommend success", zap.Int64("id", conf.ID), zap.Int("recommendType", conf.SupplementRecommendType),
			zap.Int("supplementFetchCount", supplementFetchCount), zap.Int("actualCount", len(list)), zap.Error(err))
	}()

	// RPC 调用补量系统服务，获取推荐列表
	list, err = rpcGetSupplementRecommendViewItems(conf.Terminal, supplementdao.RecommendType(conf.SupplementRecommendType))
	if err != nil {
		return
	}
	supplementFetchCount = len(list)
	if len(list) == 0 {
		return
	}
	if onlineInfo != nil { // 过滤非在线的, 并对齐在线信息
		list, unrecommendList = filterOfflineAndAlignOnlineInfo(platform, conf, list, onlineInfo)
		if len(list) == 0 {
			return
		}
	}

	// 统一规则过滤, 基础信息填充
	var failed []*notRecommendItem
	// mode没填？
	list, failed = getViewItem(materials, list)
	if len(failed) > 0 {
		unrecommendList = append(unrecommendList, failed...)
	}
	return
}

// 过滤下线频道并对齐在线频道信息
func filterOfflineAndAlignOnlineInfo(platform int, conf *mgodao.RecommendTabConfig, list []*viewItem, oi *onlineInfo) (
	nList []*viewItem, unrecommendList []*notRecommendItem) {

	// 过滤非在线的
	playTagMap, _ := getRoomPlayTag(conf.Style)

	for _, item := range list {
		if c := oi.getChannel(item.UID); c.Sid == item.Sid && c.Ssid == item.Ssid {
			item.ZoneID = conf.ID
			item.Platform = platform
			attributesIcon, _ := playTagMap[toPlayTagKey(item.RoomType, item.GameType)]
			item.AttributesIcon = getImageURL(conf.Terminal, attributesIcon)
			item = alignSupplementViewItemOnlineInfo(c, oi, item)
			nList = append(nList, item)
			continue
		}
		yylog.Debug("cancel supplement item, offline", fts.UID(item.UID), fts.SSID(item.Sid), fts.SID(item.Ssid))
		unrecommendList = append(unrecommendList, &notRecommendItem{
			UID:    item.UID,
			Sid:    item.Sid,
			Ssid:   item.Ssid,
			ZoneID: item.ZoneID,
			Reason: "未开播",
		})
	}
	return
}

// 对齐推荐系统的在线频道信息，如模板、嘉宾等
func alignSupplementViewItemOnlineInfo(c Channel, oi *onlineInfo, item *viewItem) *viewItem {
	if item.TemplateID == 0 {
		item.TemplateID = getTemplateID(item.Business)
	}
	item.GameID = oi.getGameID(c)
	item.GameType = oi.getGameType(c)
	item.GameName = oi.getGameName(c)
	item.GuestUID = oi.getChannelGuest(c)
	item.OnlineInfo = oi.getGuestList(c)
	item.RoomType = oi.getRoomType(c)
	item.RoomClass = oi.getRoomClass(c)
	item.RoomNo = oi.getRoomNo(c)
	item.RoomName = oi.getRoomName(c)
	item.RoomCover = oi.getRoomCover(c)
	item.SubRoomClass = oi.getSubRoomClass(c)
	item.LayoutConfig = oi.getLayoutConfig(c)

	// 优先使用在线频道信息里面的
	item.Icon = getFirstNotEmptyString(oi.getIcon(c), item.Icon)
	item.BigIcon = getFirstNotEmptyString(oi.getBigIcon(c), item.BigIcon)

	item.Match = match{
		UID:      oi.getUID(oi.getVsChannel(c)),
		Sid:      oi.getVsChannel(c).Sid,
		Ssid:     oi.getVsChannel(c).Ssid,
		GuestUID: oi.getChannelGuest(oi.getVsChannel(c)),
	}
	return item
}

// 协议 fts_supplement.thrift
func rpcGetSupplementRecommendViewItems(terminal int, recommendType supplementdao.RecommendType) (list []*viewItem, err error) {
	r, err := supplementdao.GetRecommendInfo(context.Background(), &fts_supplement.GetRecommendInfoReq{
		Terminal:      int32(terminal),
		RecommendType: int32(recommendType),
	})
	if err != nil || r == nil {
		yylog.Error("get slide bypass err", zap.Error(err))
		return nil, err
	}

	if len(r.GetMsg()) > 0 {
		return nil, fmt.Errorf("%s", r.GetMsg())
	}
	var vi []*viewItem
	for _, it := range r.GetInfo() {
		vi = append(vi, &viewItem{
			UID:                  it.UID,
			Sid:                  it.Sid,
			Ssid:                 it.Ssid,
			Asid:                 it.Asid,
			Psu:                  it.PopularValue,
			Title:                it.Title,
			Nick:                 it.Nick,
			Cover:                it.Cover,
			Label:                it.Label,
			Attributes:           it.Attributes,
			Weight:               it.Weight,
			Sex:                  int(it.Sex),
			Business:             int(it.Business),
			Platform:             int(it.Platform),
			GameType:             int(it.GameType),
			TemplateID:           it.TemplateId,
			SubTitle:             it.SubTitle,
			RoomType:             int(it.RoomType),
			RoomClass:            int(it.RoomClass),
			AttributesStatus:     it.AttributeStatus,
			SideAttributesStatus: it.SideAttributesStatus,
			EnterType:            enterTypeSupplement, // 补量推荐
			Mode:                 int(it.Way),         // 推荐方式 1：按uid推荐 2：按厅推荐
		})
	}

	return vi, nil
}

// 优先推荐位置数据、按位置排序、同一位置按权重排序
// 自动推荐的数据作为补充数据，排在最后, 按人气值排序
func sortCandidateViewItem(id int64, items []*viewItem) []*viewItem {
	if len(items) == 0 {
		return items
	}
	var left []*viewItem
	var right []*viewItem
	for _, item := range items {
		if item.ZoneID != id { // zoneid = tabid + position
			left = append(left, item)
		} else {
			right = append(right, item)
		}
	}
	yylog.Info("sort candidate view item", zap.Int64("id", id), zap.Int("len(items)", len(items)),
		zap.Int("len(left)", len(left)), zap.Int("len(right)", len(right)))
	sort.Slice(left, func(i, j int) bool {
		if left[i].Position != left[j].Position {
			return left[i].Position < left[j].Position
		}
		rmc := getRecommendManageConfig(left[i].RecommendManageID)
		if sortType(rmc.Sort) == sortByPsu {
			return left[i].Psu > left[j].Psu
		}
		return left[i].Weight > left[j].Weight
	})

	sort.Slice(right, func(i, j int) bool {
		return right[i].Psu > right[j].Psu
	})
	left = append(left, right...)
	return left
}

// 生成位置推荐、自动推荐 等列表
func getConfigPositionAndAutoRecommendViewItem(materials *recommendMaterials) ([]*viewItem, []*notRecommendItem) {
	conf, platform, items, oi, filter := materials.conf, materials.platform, materials.items, materials.oi(), materials.filter
	var positionItems []*mgodao.RecommendItem
	var fill []*mgodao.RecommendItem

	configPositionChannelMap := make(map[string]bool)
	configFillChannelMap := make(map[string]bool)
	setConfigChannel := func(position int64, c Channel) {
		id := fmt.Sprintf("%d:%d:%d", position, c.Sid, c.Ssid)
		if position > 0 {
			configPositionChannelMap[id] = true
		} else {
			configFillChannelMap[id] = true
		}
	}
	hasConfigChannel := func(position int64, c Channel) bool {
		id := fmt.Sprintf("%d:%d:%d", position, c.Sid, c.Ssid)
		if position > 0 {
			return configPositionChannelMap[id]
		}
		return configFillChannelMap[id]
	}
	for _, item := range items {
		if item.IsRecommendChannel() {
			c := Channel{Sid: item.Sid, Ssid: item.Ssid}
			setConfigChannel(item.Position, c)
		}
		if item.Position > 0 {
			positionItems = append(positionItems, item)
		} else {
			fill = append(fill, item)
		}
	}
	yylog.Info("recommend view item", zap.Int64("id", conf.ID), zap.Int("platform", platform),
		zap.Int("len(items)", len(items)), zap.Int("len(positionItems)", len(positionItems)),
		zap.Int("len(fill)", len(fill)), zap.Int("len(configPositionChannelMap)", len(configPositionChannelMap)))

	var tmp []*viewItem
	var failed []*notRecommendItem
	now := time.Now().Unix()
	candidateViewItem := func(items []*mgodao.RecommendItem, viewType viewType) {
		seen := make(map[string]bool)
		for _, item := range items {
			var uid int64
			var sid int64
			var ssid int64
			f := func(cuid, csid, cssid int64) {
				uid = cuid
				sid = csid
				ssid = cssid
			}
			yylog.Debug("candidate item", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int64("zoneId", item.ZoneID),
				zap.Int("mode", item.Mode), zap.Any("item", item))
			if item.IsRecommendChannel() {
				c := Channel{Sid: item.Sid, Ssid: item.Ssid}
				f(oi.getUID(c), item.Sid, item.Ssid)
			} else {
				c := oi.getChannel(item.UID)
				if hasConfigChannel(item.Position, c) { // 按厅推荐的优先级高于按人推荐
					continue
				}
				f(item.UID, c.Sid, c.Ssid)
			}
			if uid == 0 || sid == 0 || ssid == 0 {
				failed = append(failed, &notRecommendItem{
					UID:    uid,
					Sid:    sid,
					Ssid:   ssid,
					ZoneID: item.ZoneID - item.Position,
					Reason: "",
				})
				yylog.Warn("not online", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int64("zoneId", item.ZoneID),
					zap.Int("mode", item.Mode), zap.Any("item", item))
				continue
			}
			c := Channel{Sid: sid, Ssid: ssid}
			if oi.getBusiness(c) != item.Business {
				failed = append(failed, &notRecommendItem{
					UID:    uid,
					Sid:    sid,
					Ssid:   ssid,
					ZoneID: item.ZoneID - item.Position,
					Reason: "业务类型不一致",
				})
				yylog.Warn("business not same", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int64("zoneId", item.ZoneID),
					zap.Int("business", oi.getBusiness(c)), zap.Int("mode", item.Mode), zap.Any("item", item))
				continue
			}
			if !isRecommendGameType(oi.getGameType(c), materials.conf.GameType) || !isRecommendRoomType(oi.getRoomType(c), materials.conf.RoomType) {
				failed = append(failed, &notRecommendItem{
					UID:    uid,
					Sid:    sid,
					Ssid:   ssid,
					ZoneID: item.ZoneID - item.Position,
					Reason: "玩法或房间类型不一致",
				})
				yylog.Warn("room type or game type not same", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int64("zoneId", item.ZoneID),
					zap.Int("mode", item.Mode), zap.Any("item", item))
				continue
			}
			// 2022-03-11, 对YY开黑房做特殊处理, 和打通房一样允许运营配在不同的推荐逻辑
			if item.Business != businessBasic && item.Business != businessGameTeam && (filter != nil && filter(c, oi)) {
				failed = append(failed, &notRecommendItem{
					UID:    uid,
					Sid:    sid,
					Ssid:   ssid,
					ZoneID: item.ZoneID - item.Position,
					Reason: "不符合推荐逻辑",
				})
				yylog.Warn("can be bot recommend", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int64("zoneId", item.ZoneID),
					zap.Int("mode", item.Mode), zap.Any("item", item))
				continue
			}
			sameZoneIDChannel := fmt.Sprintf("%d:%d:%d", item.ZoneID, sid, ssid)
			if seen[sameZoneIDChannel] {
				continue
			}
			seen[sameZoneIDChannel] = true
			tmp = append(tmp, &viewItem{
				UID:                  uid,
				Sid:                  sid,
				Ssid:                 ssid,
				ZoneID:               item.ZoneID,
				ConfZoneID:           item.ConfZoneID,
				TemplateID:           getTemplateID(item.Business),
				Weight:               item.Weight,
				Platform:             item.Platform,
				Position:             item.Position,
				Business:             item.Business,
				GameType:             oi.getGameType(c),
				LabelExpired:         item.LabelExpired(now),
				ShowAttributesStatus: item.IsShowAttributesStatus(),
				Mode:                 item.Mode,
				Icon:                 oi.getIcon(c),
				BigIcon:              oi.getBigIcon(c),
				ViewType:             viewType,
				GameID:               oi.getGameID(c),
				GameName:             oi.getGameName(c),
				GuestUID:             oi.getChannelGuest(c),
				OnlineInfo:           oi.getGuestList(c),
				RoomType:             oi.getRoomType(c),
				RoomClass:            oi.getRoomClass(c),
				Terminal:             oi.getTerminal(c),
				Nick:                 oi.getNick(c),
				RoomName:             oi.getRoomName(c),
				RoomCover:            oi.getRoomCover(c),
				AttributesStatus:     oi.getAttributesStatus(c),
				RcAttributesStatus:   oi.getRcAttributesStatus(c),
				SideAttributesStatus: oi.getSideAttributesStatus(c),
				RecommendManageID:    item.RecommendManageID,
				RoomNo:               oi.getRoomNo(c),
				SubRoomClass:         oi.getSubRoomClass(c),
				LayoutConfig:         oi.getLayoutConfig(c),
				Match: match{
					UID:      oi.getUID(oi.getVsChannel(c)),
					Sid:      oi.getVsChannel(c).Sid,
					Ssid:     oi.getVsChannel(c).Ssid,
					GuestUID: oi.getChannelGuest(oi.getVsChannel(c)),
				},
			})
		}
	}

	// 优先推荐运营配置的位置数据
	candidateViewItem(positionItems, viewTypePos)
	yylog.Info("get position view item", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int("len(items)", len(items)),
		zap.Int("len(positionItems)", len(positionItems)), zap.Int("len(tmp)", len(tmp)))

	// 自动推荐的数据作为补充数据
	candidateViewItem(fill, viewTypeAuto)
	yylog.Info("get auto view item", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int("len(items)", len(items)),
		zap.Int("len(fill)", len(fill)), zap.Int("len(positionItems)", len(positionItems)), zap.Int("len(tmp)", len(tmp)))

	vi, nri := getViewItem(materials, tmp)
	failed = append(failed, nri...)
	yylog.Info("get view item", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Int("len(items)", len(items)),
		zap.Int("len(fill)", len(fill)), zap.Int("len(positionItems)", len(positionItems)), zap.Int("len(vi)", len(vi)))
	return sortCandidateViewItem(conf.ID, vi), failed
}

// 整理特定业务类型下的推荐列表, 返回结果按照人气值排序并根据uid去重
func getOnlineInfoViewItem(materials *recommendMaterials) ([]*viewItem, []*notRecommendItem) {
	businessList, platform, oi, conf, filter := materials.businessList, materials.platform, materials.oi(), materials.conf, materials.filter
	var tmp []*viewItem
	var failed []*notRecommendItem
	uids := oi.getUIDListByBusinessList(businessList)
	for _, uid := range uids {
		c := oi.getChannel(uid)
		if !isRecommendGameType(oi.getGameType(c), materials.conf.GameType) || !isRecommendRoomType(oi.getRoomType(c), materials.conf.RoomType) {
			failed = append(failed, &notRecommendItem{
				UID:    uid,
				Sid:    c.Sid,
				Ssid:   c.Ssid,
				ZoneID: conf.ID,
				Reason: "玩法或房间类型不一致",
			})
			yylog.Info("room type or game type not same", zap.Int64("id", conf.ID), zap.Int("platform", platform), fts.UID(uid),
				fts.SSID(c.Sid), fts.SID(c.Ssid), zap.Int("gameType", oi.getGameType(c)), zap.Int("roomType", oi.getRoomType(c)))
			continue
		}
		if filter != nil && filter(c, oi) { // 数据过滤
			failed = append(failed, &notRecommendItem{
				UID:    uid,
				Sid:    c.Sid,
				Ssid:   c.Ssid,
				ZoneID: conf.ID,
				Reason: "不符合推荐逻辑",
			})
			yylog.Debug("can be not recommend", zap.Int64("id", conf.ID), zap.Int("platform", platform), fts.UID(uid),
				fts.SSID(c.Sid), fts.SID(c.Ssid))
			continue
		}
		business := oi.getBusiness(c)
		tmp = append(tmp, &viewItem{
			UID:                  uid,
			Sid:                  c.Sid,
			Ssid:                 c.Ssid,
			ZoneID:               conf.ID,
			ConfZoneID:           conf.ID,
			TemplateID:           getTemplateID(business),
			Platform:             platform,
			Business:             business,
			GameType:             oi.getGameType(c),
			RoomType:             oi.getRoomType(c),
			RoomClass:            oi.getRoomClass(c),
			Icon:                 oi.getIcon(c),
			BigIcon:              oi.getBigIcon(c),
			OnlineInfo:           oi.getGuestList(c),
			ShowAttributesStatus: true,
			ViewType:             viewTypeAuto,
			GameID:               oi.getGameID(c),
			GameName:             oi.getGameName(c),
			GuestUID:             oi.getChannelGuest(c),
			Terminal:             oi.getTerminal(c),
			Nick:                 oi.getNick(c),
			RoomName:             oi.getRoomName(c),
			RoomCover:            oi.getRoomCover(c),
			RoomNo:               oi.getRoomNo(c),
			AttributesStatus:     oi.getAttributesStatus(c),
			RcAttributesStatus:   oi.getRcAttributesStatus(c),
			SideAttributesStatus: oi.getSideAttributesStatus(c),
			SubRoomClass:         oi.getSubRoomClass(c),
			LayoutConfig:         oi.getLayoutConfig(c),
			Match: match{
				UID:      oi.getUID(oi.getVsChannel(c)),
				Sid:      oi.getVsChannel(c).Sid,
				Ssid:     oi.getVsChannel(c).Ssid,
				GuestUID: oi.getChannelGuest(oi.getVsChannel(c)),
			},
		})
	}
	vi, nri := getViewItem(materials, tmp)
	sort.Slice(vi, func(i, j int) bool {
		return vi[i].Psu > vi[j].Psu
	})
	yylog.Info("get online info view item", zap.Int64("id", conf.ID), zap.Int("platform", platform), zap.Ints("business", businessList),
		zap.Ints("gameType", conf.GameType), zap.Int("len(uids)", len(uids)), zap.Int("len(vi)", len(vi)), zap.Int("len(tmp)", len(tmp)))
	return vi, append(failed, nri...)
}

// 获取开黑车队下的匹配中列表
func getGameQueueViewItem(materials *recommendMaterials) (list []*viewItem) {
	platform, waitingTeams, conf := materials.platform, materials.waitingTeamList, materials.conf
	var skipSid []int64 // 因为封面为空过滤的列表
	var allChannel []Channel
	for _, item := range waitingTeams {
		allChannel = append(allChannel, Channel{Sid: item.SID, Ssid: item.SSID})
	}
	passwordMap := batchSubChannelPasswordMapInfo(allChannel)

	for _, team := range waitingTeams {
		fixURL := "https://peiwan.bs2dl.yy.com/yo_jy_default.png" // 默认头像
		terminal := materials.conf.Terminal
		matchStatus := "https://res.yy.com/fts/mobile/yo/recommend/status_matching.png" // 匹配中图标
		if team.Status == 1 {
			matchStatus = "https://res.yy.com/fts/mobile/yo/recommend/status_full.png" // 已满人图标
		}
		if len(team.Cover) == 0 {
			skipSid = append(skipSid, team.SSID)
			continue
		}
		c := Channel{Sid: team.SID, Ssid: team.SSID}
		if password, ok := passwordMap[c]; ok && password {
			yylog.Warn("skip a lock channel", zap.Any("item", c))
			continue
		}
		fixedGustList := fixGuestList(team.GuestList, terminal, fixURL)
		var leaderAvatar string
		for _, entry := range fixedGustList {
			if entry.UID == team.LeaderUID {
				leaderAvatar = entry.Avatar
			}
		}
		list = append(list, &viewItem{
			UID:              team.LeaderUID,
			Sid:              team.SID,
			Ssid:             team.SSID,
			Cover:            team.Cover,
			Title:            team.Title,
			RoomName:         team.SubTitle,
			AttributesStatus: matchStatus,
			OnlineInfo:       fixedGustList,
			Psu:              team.Online, // 频道人数=>热度
			Platform:         platform,
			ViewType:         viewTypeAuto,
			Avatar:           leaderAvatar, // 队长头像
			// CardStyle:        1, // 2.0 版本cardStyle=1样式出现问题，暂时不用
		})
	}
	// vi := getViewItem(materials, tmp)
	yylog.Info("get online info view item",
		zap.Int64("id", conf.ID), zap.Int("platform", platform),
		zap.Int64s("skipSid", skipSid),
		zap.Int("n_password", len(passwordMap)),
		zap.Int("n_list", len(list)))
	return
}

// 修复头像字段
// fixURL: 需要替换的url
func fixGuestList(before []guestInfo, terminal int, fixURL string) (after []guestInfo) {
	for i, item := range before {
		if len(item.Avatar) == 0 || item.Avatar == fixURL {
			before[i].Avatar = getDefaultAvatar(terminal)
		}
	}
	return before
}

func sink2TheBottom(conf *mgodao.RecommendTabConfig, platform int, pvi, ovi []*viewItem) []*viewItem {
	pLeft, pRight := checkSink2TheBottom(conf, platform, pvi)
	oLeft, oRight := checkSink2TheBottom(conf, platform, ovi)

	right := append([]*viewItem{}, pRight...)
	right = append(right, oRight...)
	sort.Slice(right, func(i, j int) bool {
		return right[i].Psu > right[j].Psu
	})

	r := append([]*viewItem{}, pLeft...)
	r = append(r, oLeft...)
	r = append(r, right...)
	return r
}

func checkSink2TheBottom(conf *mgodao.RecommendTabConfig, platform int, vi []*viewItem) ([]*viewItem, []*viewItem) {
	var channelInfo []*fts_live_afk.ChannelInfo
	for _, item := range vi {
		c := fts_live_afk.ChannelInfo{
			Sid:  item.Sid,
			Ssid: item.Ssid,
			UID:  item.UID,
		}
		channelInfo = append(channelInfo, &c)
	}
	var afkMap map[int64]fts_live_afk.AFKInfo
	var err error
	if conf.Afk {
		afkMap, err = liveafkdao.BatchCheckFtsLiveAFKInfoSplit(channelInfo, businessZhuiWan, 10000)
		if err != nil {
			yylog.Warn("cannot check live afk", zap.Int64("id", conf.ID),
				zap.Int("platform", platform), zap.Int("len(vi)", len(vi)), zap.Error(err))
		}
	}

	var unpopularMap map[int64]bool
	if conf.Unpopular {
		unpopularMap, err = getUnpopularList(config.ServerConfig.IsTestEnv)
		if err != nil {
			yylog.Warn("cannot get unpopular list", zap.Int64("id", conf.ID),
				zap.Int("platform", platform), zap.Int("len(vi)", len(vi)), zap.Error(err))
		}
	}

	var left, right []*viewItem
	for _, item := range vi {
		_, ok1 := afkMap[item.UID]
		_, ok2 := unpopularMap[item.UID]
		ok3 := item.Psu < conf.PopularityValueMin
		yylog.Info("check sink", zap.Int64("id", conf.ID), zap.Int("platform", platform),
			zap.Bool("ok1", ok1), zap.Bool("ok2", ok2), zap.Bool("ok3", ok3))
		if ok1 || ok2 || ok3 {
			item.ViewType = viewTypeSink
			right = append(right, item)
		} else {
			left = append(left, item)
		}
	}
	return left, right
}

// 生成推荐列表 (位置推荐列表+特定取数逻辑列表) (filter:返回true表示过滤该条数据)
func getAllViewItem(materials *recommendMaterials) ([]*viewItem, []*notRecommendItem) {
	var ovi []*viewItem
	var onri []*notRecommendItem
	if materials.recommendOnlineInfo { // 根据业务类型取在线频道
		ovi, onri = getOnlineInfoViewItem(materials)
	}
	if materials.conf.Logic == recommendGameQueue { // 取开黑车队等待列表
		ovi = getGameQueueViewItem(materials)
	}
	pvi, pnri := getConfigPositionAndAutoRecommendViewItem(materials) // 位置推荐列表
	yylog.Info("get all view item", zap.Int64("id", materials.conf.ID), zap.Int("platform", materials.platform),
		zap.Ints("business", materials.businessList), zap.Int("len(ovi)", len(ovi)), zap.Int("len(pvi)", len(pvi)))
	return append(pvi, ovi...), append(onri, pnri...)
}

func notNewCompere() func(c Channel, oi *onlineInfo) bool {
	ret, err := compereinfodao.QueryNewCompereByAppIDTime(int32(contract.TAppId_Dating), 0, 0)
	if err != nil {
		yylog.Error("cannot query new compere", zap.Error(err))
	}
	var m map[int64]bool
	if ret != nil {
		for uid := range ret.NewCompereInfoRet_ {
			if m == nil {
				m = make(map[int64]bool)
			}
			m[uid] = true
		}
	}
	yylog.Debug("new compere", zap.Any("m", m))
	return func(c Channel, oi *onlineInfo) bool {
		uid := oi.getUID(c)
		return !m[uid]
	}
}

func newRecommendMaterials(platform int,
	items []*mgodao.RecommendItem,
	oic *onlineInfoCategory,
	conf *mgodao.RecommendTabConfig,
	waitingTeam []waitingTeamInfo) *recommendMaterials {
	switch conf.Logic {
	case recommendByJYOnlineInfo:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			recommendOnlineInfo: true,
			businessList:        []int{businessJY},
		}
	case recommendByPKOnlineInfo:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			recommendOnlineInfo: true,
			businessList:        []int{businessPK},
		}
	case recommendByBabyOnlineInfo:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			recommendOnlineInfo: true,
			businessList:        []int{businessBaby},
		}
	case recommendVideoDatingGameType:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessJY},
			recommendOnlineInfo: true,
			filter: func(c Channel, oi *onlineInfo) bool {
				return oi.getGameType(c) != constinfo.KVideoDating
			},
		}
	case recommendGameTeam:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessGame},
			recommendOnlineInfo: true,
			filter: func(c Channel, oi *onlineInfo) bool {
				return oi.getPlayType(c) != playTypeGameTeam
			},
		}
	case recommendChatRoom:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessGame},
			recommendOnlineInfo: true,
			filter: func(c Channel, oi *onlineInfo) bool {
				return oi.getPlayType(c) != playTypeChatRoom || isGameIDMatchGoodOrGoddess(oi.getGameID(c))
			},
		}
	case recommendByConfig, recommendHitHotInfo, recommendByWeight:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        nil,
			recommendOnlineInfo: false,
			filter: func(c Channel, oi *onlineInfo) bool {
				return conf.Category == categoryCloudGame && !conf.IsRecommendGameID(oi.getGameID(c))
			},
		}
	case recommendVideo:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoVideo,
			conf:                conf,
			businessList:        nil,
			recommendOnlineInfo: true,
		}
	case recommendAudio:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAudio,
			conf:                conf,
			businessList:        nil,
			recommendOnlineInfo: true,
		}
	case recommendNewCompere:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessJY, businessPK, businessBaby},
			recommendOnlineInfo: true,
			filter:              notNewCompere(),
		}
	case recommendHonorOfKings: // 王者荣耀
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessGame},
			recommendOnlineInfo: true,
			filter: func(c Channel, oi *onlineInfo) bool {
				return !isGameIDMatchWZRY(oi.getGameID(c))
			},
		}
	case recommendPUBGMobile: // 和平精英
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessGame},
			recommendOnlineInfo: true,
			filter: func(c Channel, oi *onlineInfo) bool {
				return !isGameIDMatchHPJY(oi.getGameID(c))
			},
		}
	case recommendTeamFightTactics: // 云顶之弈
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessGame},
			recommendOnlineInfo: true,
			filter: func(c Channel, oi *onlineInfo) bool {
				return !isGameIDMatchYDZY(oi.getGameID(c))
			},
		}
	case recommendMiniGame:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			businessList:        []int{businessGame},
			recommendOnlineInfo: true,
			filter: func(c Channel, oi *onlineInfo) bool {
				return oi.getPlayType(c) != playTypeMiniGame
			},
		}
	case recommendBySupplement:
		return &recommendMaterials{
			platform: platform,
			oic:      oic,
			t:        onlineInfoAll,
			conf:     conf,
		}
	case recommendReception:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			recommendOnlineInfo: true,
			businessList:        []int{businessGameTeam},
		}
	case recommendAllOnlineInfo:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			recommendOnlineInfo: true,
		}
	case recommendSkillCard:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			recommendOnlineInfo: true,
			businessList:        []int{businessSkillCard},
		}
	case recommendByJYAndBabyOnlineInfo:
		return &recommendMaterials{
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoAll,
			conf:                conf,
			recommendOnlineInfo: true,
			businessList:        []int{businessJY, businessBaby},
		}
	case recommendGameQueue:
		return &recommendMaterials{
			conf:                conf,
			platform:            platform,
			items:               items,
			oic:                 oic,
			t:                   onlineInfoGame,
			recommendOnlineInfo: false,
			businessList:        []int{businessNone},
			waitingTeamList:     waitingTeam,
		}
	default:
	}
	return nil
}

// 构建并缓存推荐列表
func buildRecommendInfo(materials *recommendMaterials) {
	if materials == nil {
		return
	}
	var vi []*viewItem
	var failed []*notRecommendItem
	const maxTry = 3
	for try := 0; try < maxTry; try++ {
		var err error
		if materials.conf.Logic != recommendBySupplement {
			vi, failed = getAllViewItem(materials)
		} else {
			// 使用补量推荐逻辑，从补量系统获取推荐列表
			vi, failed, err = getSupplementRecommendViewItems(materials)
		}
		if err != nil { // 异常重试
			continue
		}
		yylog.Info("build recommend info", zap.Int64("id", materials.conf.ID), zap.Int("platform", materials.platform),
			zap.Int("len(items)", len(materials.items)), zap.Any("len(vi)", len(vi)), zap.Int("minSize", materials.conf.MinSize))
		if len(vi) >= materials.conf.MinSize {
			break
		}
	}

	// 测试环境实时更新，线上环境推荐数量为0时，不直接更新缓存，防止推荐的数据为空
	if !config.ServerConfig.IsTestEnv && len(vi) == 0 && materials.conf.Logic != recommendHitHotInfo {
		if materials.conf.MinSize > 0 {
			go func() {
				sendNoticeToRobot(materials.conf.Terminal, materials.conf.Name, materials.conf.ID, materials.platform, materials.conf.MinSize, len(vi))
			}()
		}
		return
	}

	go func() {
		setRecommendViewItem(materials.conf.ID, materials.platform, vi)
	}()

	go func() {
		removeNotRecommendItemFeatureInfo(failed)
		setNotRecommendItem(materials.conf.ID, materials.platform, failed)
	}()

	go func() {
		setRecommendFeatureInfo(vi)
	}()
}

func removeNotRecommendItemFeatureInfo(items []*notRecommendItem) {
	if len(items) == 0 {
		return
	}
	var channelList []string
	for _, item := range items {
		channelList = append(channelList, toChannelStr(item.Sid, item.Ssid))
	}
	if err := redisdao.RemoveRecommendFeatureInfo(channelList); err != nil {
		yylog.Error("failed remove recommend feature info", zap.Error(err))
	}
}

func platformName(p int) string {
	switch p {
	case platformAndroid:
		return "android"
	case platformIOS:
		return "ios"
	case platformAndroidAndIOS:
		return "android_ios"
	case platformPC:
		return "PC"
	case platformPCAndAPP:
		return "android_ios_pc"
	default:
		return strconv.FormatInt(int64(p), 10)
	}
}

func sendNoticeToRobot(terminal int, name string, id int64, platform, minSize, size int) {
	now := time.Now()
	var businessMsg []string
	for _, business := range getAllBusiness() {
		v, _ := businessCount.Load(business)
		count, _ := v.(int)
		businessMsg = append(businessMsg, fmt.Sprintf("> 当前全网的%s业务开播总量: <font color=\"info\">%d</font>",
			getBusinessChineseName(business), count))
	}

	var msgTemplate string
	if config.ServerConfig.IsTestEnv {
		msgTemplate += "> 环境：测试环境\n"
	} else {
		msgTemplate += "> 环境：正式环境\n"
	}
	msgTemplate += "> 推荐终端: <font color=\"info\">%s</font>\n" +
		"> id: %d\n" +
		"> TAB: %s\n" +
		"> 平台: %s\n" +
		"> 当前推荐数量: <font color=\"info\">%d</font>\n" +
		"> 最小推荐数量: <font color=\"warning\">%d</font>\n" +
		"%s\n" +
		"> 时间: %s"

	msg := fmt.Sprintf(msgTemplate, getTerminalName(terminal), id, name, platformName(platform),
		size, minSize, strings.Join(businessMsg, "\n"), now.Format("2006-01-02 15:04:05"))

	s, err := redisdao.GetRecommendWarningTime(id, platform)
	if err != nil {
		yylog.Error("cannot get recommend warning time", zap.Error(err))
	}
	if len(s) > 0 {
		return
	}

	// 避免告警太多
	// 晚上23点以后，同一平台推荐位的告警20分钟一条
	// 9点到23点，3分钟一条
	var ttl int64
	if now.Hour() > 9 && now.Hour() < 23 {
		ttl = 180
	} else {
		ttl = 1200
	}
	if err := redisdao.SetRecommendWarningTime(id, platform, fmt.Sprintf("%d", now.Unix()), ttl); err != nil {
		yylog.Error("cannot set recommend warning time", zap.Error(err))
	}

	robot := wchat.ZhuiyaRecommendRobot
	if terminal == pcGameTerminal {
		robot = wchat.PCGameRecommendRobot
	}
	// id=32200女团tab的告警可以取消了哈，这个导航应该是很久之前配的
	// 后台现在都看不到 @陈敏仪
	if id == 32200 {
		yylog.Warn("", zap.String("alertContent", msg))
		return
	}
	if !config.ServerConfig.IsTestEnv {
		if err := wchat.SendRobotMsgMarkdownWithKey(robot, msg); err != nil {
			yylog.Error("cannot send robot msg", zap.Error(err))
		}
	}

	// 发一份到如流 群名：yo交友推荐告警
	const notifyRobotKey = "d574b78c2d63bf021270a95f8e817c941"
	infoFlowMsg := infoflowagentdao.CreateMessage(infoflowagentdao.EleMD(msg))
	robotRet, err := infoflowagentdao.SendMessage(notifyRobotKey, &infoFlowMsg)
	if err != nil {
		yylog.ErrorWithBam("infoFlowMessageErr",
			zap.Any("msgInfo", msg),
			zap.Reflect("robotRet", robotRet),
			zap.Error(err))
		return
	}
	yylog.Info("infoFlowMessage",
		zap.Any("msgInfo", msg),
		zap.Reflect("robotRet", robotRet),
		zap.Error(err))
}

// 设置推荐信息的缓存
func setRecommendViewItem(id int64, platform int, items []*viewItem) {
	b, err := json.Marshal(items)
	if err != nil {
		yylog.Error("cannot marshal json", zap.Error(err), zap.Int64("id", id), zap.Int("platform", platform))
		return
	}
	if err := redisdao.SetRecommendViewItemCache(id, platform, string(b)); err != nil {
		yylog.Error("cannot set recommend view item cache", zap.Error(err), zap.Int64("id", id), zap.Int("platform", platform))
	}
}

func setNotRecommendItem(id int64, platform int, items []*notRecommendItem) {
	b, err := json.Marshal(items)
	if err != nil {
		yylog.Error("cannot marshal json", zap.Error(err), zap.Int64("id", id), zap.Int("platform", platform))
		return
	}
	if err := redisdao.SetNotRecommendItemCache(id, platform, string(b)); err != nil {
		yylog.Error("cannot set recommend view item cache", zap.Error(err), zap.Int64("id", id), zap.Int("platform", platform))
	}
}

func setRecommendFeatureInfo(items []*viewItem) {
	featureInfoMap := make(map[string]string)
	for _, item := range items {
		featureInfo := fts_zhuiya_recommend_v2.RecommendFeatureInfo{
			Sid:      item.Sid,
			Ssid:     item.Ssid,
			UID:      item.UID,
			Title:    item.Title,
			Cover:    item.Cover,
			Psu:      item.Psu,
			Business: int32(item.Business),
			RoomNo:   item.RoomNo,
			RoomName: item.RoomName,
			Nick:     item.Nick,
			Avatar:   item.Avatar,
		}
		b, err := json.Marshal(featureInfo)
		if err != nil {
			yylog.Error("cannot marshal json", zap.Any("info", featureInfo))
			continue
		}
		featureInfoMap[toChannelStr(item.Sid, item.Ssid)] = string(b)
	}
	if err := redisdao.BatchSetRecommendFeatureInfo(featureInfoMap); err != nil {
		yylog.Error("cannot batch set recommend feature info", zap.Error(err))
	}
}

// 获取推荐信息的缓存
func getRecommendViewItem(ctx context.Context, id int64, platform int) ([]*viewItem, error) {
	s, err := redisdao.GetRecommendViewItemCache(id, platform)
	if err != nil {
		return nil, err
	}
	if len(s) == 0 {
		return nil, nil
	}
	var ret []*viewItem
	err = json.Unmarshal([]byte(s), &ret)
	if err != nil {
		return nil, err
	}
	yylog.Debug("get recommend view item", fts.TraceID(ctx),
		zap.Int64("id", id), zap.Int("platform", platform), zap.Int("len(ret)", len(ret)))
	return ret, nil
}

func getNotRecommendItem(ctx context.Context, id int64, platform int, disabledMigrate int) ([]*notRecommendItem, error) {
	if disabledMigrate == 2 || (disabledMigrate != 1 && migrate.isMigrate(id)) {
		return migrate.getUnRecommendList(ctx, id, platform)
	}
	s, err := redisdao.GetNotRecommendViewCache(id, platform)
	if err != nil {
		return nil, err
	}
	if len(s) == 0 {
		return nil, nil
	}
	var ret []*notRecommendItem
	err = json.Unmarshal([]byte(s), &ret)
	if err != nil {
		return nil, err
	}
	yylog.Debug("get recommend view item", fts.TraceID(ctx),
		zap.Int64("id", id), zap.Int("platform", platform), zap.Int("len(ret)", len(ret)))
	return ret, nil
}

// 根据房间的人气值进行排序
func sortViewItemByPsu(id int64, terminal int, items []*viewItem) []*viewItem {
	if len(items) == 0 {
		return items
	}
	var channels []Channel
	for _, item := range items {
		channels = append(channels, Channel{
			Sid:  item.Sid,
			Ssid: item.Ssid,
		})
	}
	popularityMap, err := getRoomPopularityValue(id, terminal, channels)
	if err != nil || popularityMap == nil {
		yylog.Warn("cannot get room popularity value", zap.Any("popularityMap", popularityMap),
			zap.Int("len(items)", len(items)), zap.Error(err))
		return items
	}
	for _, item := range items {
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		item.Psu = popularityMap[c]
		yylog.Debug("sort view item by psu", zap.Int64("id", id), zap.Any("c", c), zap.Int64("psu", item.Psu))
	}
	sort.Slice(items, func(i, j int) bool {
		return items[i].Psu > items[j].Psu
	})
	return items
}

// 从缓存中获取推荐列表并进行沉底和排序等处理
func getTabRecommendInfo(ctx context.Context, id int64, platform, terminal int, psuSort bool) ([]*viewItem, error) {
	items, err := getRecommendViewItem(ctx, id, platform)
	if err != nil {
		return nil, err
	}
	if len(items) == 0 {
		return items, nil
	}

	var left, right, sink []*viewItem

	tail := len(items) - 1
	for ; tail >= 0; tail-- {
		if items[tail].ViewType != viewTypeSink {
			break
		} else if isRecommendTerminal(terminal, items[tail].Terminal) {
			sink = append(sink, items[tail])
		}
	}

	for ; tail >= 0; tail-- {
		if items[tail].ViewType != viewTypeAuto {
			break
		}
	}
	for _, item := range items[:tail+1] {
		if isRecommendTerminal(terminal, item.Terminal) {
			left = append(left, item)
		}
	}
	if tail < len(items)-len(sink) {
		for _, item := range items[tail+1 : len(items)-len(sink)] {
			if isRecommendTerminal(terminal, item.Terminal) {
				right = append(right, item)
			}
		}
	}
	yylog.Debug("get tab recommend info", zap.Int64("id", id), zap.Int("platform", platform),
		zap.Int("len(left)", len(left)), zap.Int("len(right)", len(right)), zap.Int("len(sink)", len(sink)))
	if psuSort {
		right = sortViewItemByPsu(id, terminal, right)
		sink = sortViewItemByPsu(id, terminal, sink)
	}
	return sortViewItemByPositionAndWeight(id, left, right, sink), nil
}

// 相同权重随机排序
func randSameWeight(start, end int, items []*viewItem, ret []*viewItem) []*viewItem {
	n := end - start
	if n > 1 {
		indexes := rand.Perm(n)
		for _, index := range indexes {
			ret = append(ret, items[start+index])
		}
	} else if n == 1 {
		ret = append(ret, items[start])
	}
	return ret
}

func sortViewItemByWeight(items []*viewItem) []*viewItem {
	if len(items) <= 1 {
		return items
	}
	sort.Slice(items, func(i, j int) bool {
		return items[i].Weight > items[j].Weight
	})
	start := 0
	var ret []*viewItem
	for i := 1; i < len(items); i++ {
		if items[start].Weight == items[i].Weight {
			continue
		}
		ret = randSameWeight(start, i, items, ret)
		start = i
	}
	ret = randSameWeight(start, len(items), items, ret)
	return ret
}

func getWeightRecommendInfo(ctx context.Context, id int64, platform int, terminal int) ([]*viewItem, error) {
	items, err := getRecommendViewItem(ctx, id, platform)
	if err != nil {
		return nil, err
	}
	var vi, sink []*viewItem
	seen := make(map[Channel]bool)
	for _, item := range items {
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		if seen[c] && !isRecommendTerminal(terminal, item.Terminal) {
			continue
		}
		seen[c] = true
		if item.ViewType == viewTypeSink {
			sink = append(sink, item)
		} else {
			vi = append(vi, item)
		}
	}
	vi = sortViewItemByWeight(vi)                // 按权重排
	sink = sortViewItemByPsu(id, terminal, sink) // 按人气排
	vi = append(vi, sink...)
	return vi, nil
}

func getWonderfulLiveRecommendInfo(ctx context.Context, id int64, platform, terminal int) ([]*viewItem, error) {
	var wg sync.WaitGroup
	var vi []*viewItem
	var mutex sync.Mutex
	seen := make(map[Channel]bool)
	for _, tabID := range []int64{partyListTabID, multiVideoTabID, superCompereTabID} {
		tabID := tabID
		wg.Add(1)
		gfy.Go(ctx, func(ctx context.Context) int {
			defer wg.Done()
			items, err := getRecommendViewItem(ctx, tabID, platform)
			if err != nil {
				return 0
			}
			for _, item := range items {
				mutex.Lock()
				c := Channel{Sid: item.Sid, Ssid: item.Ssid}
				if !seen[c] {
					item.ZoneID = id
					item.Position = 0
					vi = append(vi, item)
					seen[c] = true
				}
				mutex.Unlock()
			}
			yylog.Debug("get recommend view item", fts.TraceID(ctx), zap.Int64("id", id), zap.Int("platform", platform),
				zap.Int64("tabId", tabID), zap.Int("len(items)", len(items)))
			return 0
		})
	}
	wg.Wait()
	yylog.Debug("wonderful live recommend info", fts.TraceID(ctx), zap.Int64("id", id), zap.Int("platform", platform), zap.Int("terminal", terminal),
		zap.Int("size", len(vi)))
	return sortViewItemByPsu(id, terminal, vi), nil
}

func getTopListRecommendInfo(ctx context.Context, id int64, platform, terminal int) ([]*viewItem, error) {
	var m sync.Map
	var wg sync.WaitGroup
	for _, tabID := range []int64{id, partyListTabID, multiVideoTabID} {
		tabID := tabID
		wg.Add(1)
		gfy.Go(ctx, func(ctx context.Context) int {
			defer wg.Done()
			items, err := getRecommendViewItem(ctx, tabID, platform)
			m.Store(tabID, items)
			yylog.Debug("get recommend view item", fts.TraceID(ctx), zap.Int64("id", id), zap.Int("platform", platform),
				zap.Int64("tabId", tabID), zap.Int("len(items)", len(items)), zap.Error(err))
			return 0
		})
	}
	wg.Wait()

	var position []*viewItem
	var fill []*viewItem
	seen := make(map[Channel]bool)
	m.Range(func(k, v any) bool {
		configID, ok := k.(int64)
		if !ok {
			return true
		}
		items, ok := v.([]*viewItem)
		if !ok {
			return true
		}
		// 按位置推荐
		if configID == id {
			position = append(position, items...)
			return true
		}
		for _, item := range items {
			if item == nil {
				continue
			}
			c := Channel{Sid: item.Sid, Ssid: item.Ssid}
			if !seen[c] {
				item.ZoneID = id
				item.Position = 0
				fill = append(fill, item)
				seen[c] = true
			}
		}
		return true
	})
	yylog.Debug("top list recommend info", fts.TraceID(ctx), zap.Int64("id", id), zap.Int("platform", platform), zap.Int("terminal", terminal),
		zap.Int("len(position)", len(position)), zap.Int("len(fill)", len(fill)))
	return sortViewItemByPositionAndWeight(id, position, sortViewItemByPsu(id, terminal, fill), nil), nil
}

// 取对应分类每个tab排在前面几位对应的数据
// everyTabMaxSize为0，表示取对应分类每个tab的所有数据
func getHitHotRecommendInfo(ctx context.Context, id int64, platform, terminal, category int, version fts.Version, everyTabMaxSize int) ([]*viewItem, error) {
	var m sync.Map
	var wg sync.WaitGroup
	recommendTabConfig.Range(func(k, v any) bool {
		conf, ok := v.(mgodao.RecommendTabConfig)
		if !ok {
			return true
		}
		query := tabInfoReq{
			Category: category,
			Platform: platform,
		}
		if !isRecommendTab(&conf, &query, terminal, version) {
			return true
		}
		wg.Add(1)
		gfy.Go(ctx, func(ctx context.Context) int {
			func(configID int64, platform int) {
				defer wg.Done()
				items, err := getRecommendViewItem(ctx, configID, platform)
				m.Store(configID, items)
				yylog.Info("hit hot view item", fts.TraceID(ctx), zap.Int64("id", id), zap.Int("platform", platform),
					zap.Int64("configId", configID), zap.Int("len(items)", len(items)), zap.Error(err))
			}(conf.ID, platform)
			return 0
		})
		return true
	})
	wg.Wait()

	var position []*viewItem
	var fill []*viewItem
	seen := make(map[Channel]bool)
	m.Range(func(k, v any) bool {
		configID, ok := k.(int64)
		if !ok {
			return true
		}
		items, ok := v.([]*viewItem)
		if !ok {
			return true
		}
		// 按位置推荐
		if configID == id {
			position = append(position, items...)
			return true
		}
		size := 0
		for _, item := range items {
			if item == nil || !isRecommendTerminal(terminal, item.Terminal) {
				continue
			}
			c := Channel{Sid: item.Sid, Ssid: item.Ssid}
			if !seen[c] {
				item.ZoneID = id
				item.Position = 0
				fill = append(fill, item)
				size++
				seen[c] = true
			}
			yylog.Debug("hit hot recommend info", fts.TraceID(ctx), zap.Int64("id", item.ZoneID), zap.Int("platform", platform),
				zap.Int("size", size), zap.Any("item", item))
			// everyTabMaxSize为0，表示取对应分类每个tab的所有数据
			// 沉底的不推荐
			if everyTabMaxSize != 0 && size >= everyTabMaxSize {
				break
			}
		}
		return true
	})
	yylog.Debug("recommend info", fts.TraceID(ctx), zap.Int64("id", id), zap.Int("platform", platform), zap.Int("terminal", terminal),
		zap.Int("len(position)", len(position)), zap.Int("len(fill)", len(fill)))
	return sortViewItemByPositionAndWeight(id, position, sortViewItemByPsu(0, terminal, fill), nil), nil
}

func getNextPageViewItem(vi []*viewItem, platform, next int) ([]*viewItem, int) {
	pageSize := 30
	if platform == platformPC {
		pageSize = 150
	}
	retNext := -1
	if next >= 0 {
		nextPage := next + pageSize
		if len(vi) > nextPage { // 分页
			vi = vi[next:nextPage]
			retNext = nextPage
		} else if len(vi) > next {
			vi = vi[next:]
		}
	}
	return vi, retNext
}

// 查询补量推荐信息
func getSupplementRecommendInfo(ctx context.Context, tabID int64, platform int) ([]*viewItem, error) {
	return getRecommendViewItem(ctx, tabID, platform)
}

const zwTerminalSlideTabID = 300  // 追玩APP房间内上下滑TABID
const yoTerminalSlideTabID = 2300 // YO语音房间内上下滑TABID

// 适配推荐TABID，补量-上下滑场景下，旧版本YO语音客户端&追玩客户端传的都是 tabID=300，但是业务上后期要求 YO语音和追玩APP两者推荐内容进行区分
// 所以需要对线上YO语音旧版本（传的300）的进行适配（适配成301），仅仅后端发版
// 后续新版本 YO语音都是传的 2300，因此之前适配用的 301 就不用了，为了防止线上可能还存在旧版本的yo语音，这里保留适配逻辑
// 何清亮 朱泳嘉 协商 1.15.0 版本之后，上下滑传的都是 300，不区分终端了，300 只是作为 上下滑的标志，最终选择哪个 补量库 tabID 由后端决定
func adapterRecommendTabConfigID(tabID int64, terminal int, version fts.Version) (mappedTabID int64) {
	// 小于 1.15.0 版本的才需要打这个补丁，1.15.0 之后，传的都是 300，由 handler/v2/zw_http_handler.go:182 adapterID 决定最终 tabID
	if util.CompareVersion(version.Format(), "1.15.0") >= 0 {
		return tabID
	}
	// 2023-04-28 需要打个补丁，因为 许乐峰 看起来iOS端的Yo交友有点问题，补量库传2300(传了yo语音的的上下滑tabID)了，是同步其他的需求的时候，被改成了2300。
	if terminal == zwTerminal && tabID == yoTerminalSlideTabID { // 传错了：yo语音 & 终端是追玩
		return zwTerminalSlideTabID // 使用追玩（yo交友）的上下滑tabID 300
	}

	if tabID == zwTerminalSlideTabID && terminal == yoTerminal {
		return yoTerminalSlideTabID
	}
	return tabID
}

// 根据tabID和平台获取推荐列表
// 生成推荐列表的逻辑入口: time_handler.go=>refreshRecommendInfo()
func getRecommendInfo(ctx context.Context, id int64, platform, terminal int, version fts.Version, next int) ([]*viewItem, int, error) {
	retNext := -1
	value, ok := recommendTabConfig.Load(id)
	if !ok {
		return nil, retNext, nil
	}
	conf, ok := value.(mgodao.RecommendTabConfig) // tabID 对应的导航栏配置
	if !ok {
		return nil, retNext, nil
	}
	// yo和追玩共用一个娱乐页面
	if isYoTerminal(terminal) && (conf.Category == categoryRecreation || conf.Category == categoryInsertModule) {
		terminal = zwTerminal
	}

	yylog.Debug("get recommend info start", fts.TraceID(ctx),
		zap.Int64("id", id), zap.Int("terminal", terminal), zap.Int("platform", platform),
		zap.String("version", version.Format()), zap.Any("conf", conf))
	var vi []*viewItem
	var err error
	var inputID = id
	switch id {
	case topListTabID:
		vi, err = getTopListRecommendInfo(ctx, id, platform, terminal)
	case partyListTabID,
		multiVideoTabID,
		superCompereTabID:
		vi, err = getTabRecommendInfo(ctx, id, platform, terminal, true)
	case wonderfulLive:
		vi, err = getWonderfulLiveRecommendInfo(ctx, id, platform, terminal)
	default:
		switch conf.Logic {
		case recommendByJYOnlineInfo,
			recommendByPKOnlineInfo,
			recommendByBabyOnlineInfo,
			recommendHonorOfKings,
			recommendPUBGMobile,
			recommendTeamFightTactics:
			vi, err = getTabRecommendInfo(ctx, id, platform, terminal, true)
			vi, retNext = getNextPageViewItem(vi, platform, next)
		case recommendGameQueue:
			vi, err = getTabRecommendInfo(ctx, id, platform, terminal, false)
			vi, retNext = getNextPageViewItem(vi, platform, next)
		case recommendByConfig,
			recommendVideoDatingGameType,
			recommendGameTeam,
			recommendChatRoom,
			recommendVideo,
			recommendAudio,
			recommendNewCompere,
			recommendMiniGame,
			recommendReception,
			recommendAllOnlineInfo,
			recommendSkillCard,
			recommendByJYAndBabyOnlineInfo:
			vi, err = getTabRecommendInfo(ctx, id, platform, terminal, true)
			if util.CompareVersion(version.Format(), "2.1.0") >= 0 {
				vi, retNext = getNextPageViewItem(vi, platform, next)
			}
		case recommendHitHotInfo:
			everyTabMaxSize := 10 // 娱乐热门取每个tab的top10
			if conf.Category == categoryCloudGame {
				everyTabMaxSize = 4 // 云游戏取每个tab的top4
			}
			vi, err = getHitHotRecommendInfo(ctx, id, platform, terminal, conf.Category, version, everyTabMaxSize)
		case recommendByWeight:
			vi, err = getWeightRecommendInfo(ctx, id, platform, terminal)
		case recommendBySupplement: // 补量系统推荐逻辑
			// tabID 推荐库适配
			id = adapterRecommendTabConfigID(id, terminal, version)
			vi, err = getSupplementRecommendInfo(ctx, id, platform)
		default:
			yylog.Warn("unexpect recommend logic", fts.TraceID(ctx), zap.Int("logic", conf.Logic))
		}
	}

	// 进行红包状态设置&过滤
	applyPacketAttributeStatus(ctx, platform, terminal, version, &conf, vi)

	yylog.Debug("get recommend info end", fts.TraceID(ctx), zap.Int64("inputID", inputID),
		zap.Int64("id", id), zap.Int("terminal", terminal), zap.Int("platform", platform),
		zap.String("version", version.Format()), zap.Any("conf", conf))

	return vi, retNext, err
}

// 同一位置，选择权重最大的主持，权重越大，出现的概率越高
// 前面的位置出现过的主持不能重复出现
// 同一位置，没有对应的主持，则用自动推荐的数据填充
func getMaximumWeightItem(items []*viewItem, total int64, seen map[Channel]bool, fill []*viewItem) (*viewItem, []*viewItem) {
	if len(items) > 0 && total > 0 {
		n := rand.Int63n(total) + 1 // [1, total]
		yylog.Debug("sort", zap.Any("items", items), zap.Int64("total", total), zap.Int64("n", n))
		var sum int64
		start := 0
		for start = 0; start < len(items); start++ {
			sum += items[start].Weight
			if sum >= n {
				break
			}
		}

		if start == len(items) {
			return nil, fill // error
		}

		// weight: increase
		for i := start; i >= 0; i-- {
			c := Channel{Sid: items[i].Sid, Ssid: items[i].Ssid}
			if !seen[c] {
				return items[i], fill
			}
			yylog.Debug("sort", zap.Any("seen item", items[i]))
		}

		// weight: decreasing
		for i := start + 1; i < len(items); i++ {
			c := Channel{Sid: items[i].Sid, Ssid: items[i].Ssid}
			if !seen[c] {
				return items[i], fill
			}
			yylog.Debug("sort", zap.Any("seen item", items[i]))
		}
	}

	// weight: decreasing
	for index, item := range fill {
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		if !seen[c] {
			fill = fill[index+1:]
			return item, fill
		}
		yylog.Debug("sort", zap.Any("seen item", fill[index]))
	}
	return nil, fill
}

func getMaximumPsuItem(items []*viewItem, seen map[Channel]bool, fill []*viewItem) (*viewItem, []*viewItem) {
	for _, item := range items {
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		if !seen[c] {
			return item, fill
		}
		yylog.Debug("sort", zap.Any("seen item", item))
	}
	// psu: decreasing
	for index, item := range fill {
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		if !seen[c] {
			fill = fill[index+1:]
			return item, fill
		}
		yylog.Debug("sort", zap.Any("seen item", fill[index]))
	}
	return nil, fill
}

// 同一位置，选择权重最大的主持，权重越大，出现的概率越高
// 前面的位置出现过的主持不能重复出现
// 对应的位置，没有对应的主持，则用自动推荐的数据填充
// 同一位置的数据已经按权重由高到底排序
func sortViewItemByPositionAndWeight(id int64, left, right, sink []*viewItem) []*viewItem {
	var ret []*viewItem
	var item *viewItem
	start := 0
	end := start
	seen := make(map[Channel]bool)
	var total int64

	positionEnd := 100 // 1-99的位置可配置
	for position := 1; position < positionEnd; position++ {
		if end >= len(left) {
			break
		}
		for ; end < len(left) && left[end].ZoneID == (id+int64(position)); end++ {
			total += left[end].Weight
		}
		t := sortByWeight
		if rmc := getRecommendManageConfig(left[start].RecommendManageID); rmc != nil {
			t = sortType(rmc.Sort)
		}
		if t == sortByWeight {
			item, right = getMaximumWeightItem(left[start:end], total, seen, right)
		} else {
			item, right = getMaximumPsuItem(left[start:end], seen, right)
		}
		if item != nil {
			ret = append(ret, item)
			c := Channel{Sid: item.Sid, Ssid: item.Ssid}
			seen[c] = true
		}
		yylog.Debug("sort", zap.Int64("id", id), zap.Any("item", item))
		start = end
		total = 0
	}

	if start < len(left) {
		t := sortByWeight
		if rmc := getRecommendManageConfig(left[start].RecommendManageID); rmc != nil {
			t = sortType(rmc.Sort)
		}
		if t == sortByWeight {
			item, right = getMaximumWeightItem(left[start:end], total, seen, right)
		} else {
			item, right = getMaximumPsuItem(left[start:end], seen, right)
		}
		if item != nil {
			ret = append(ret, item)
			c := Channel{Sid: item.Sid, Ssid: item.Ssid}
			seen[c] = true
		}
		yylog.Debug("sort", zap.Int64("id", id), zap.Any("item", item))
	}

	for _, item := range right {
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		if !seen[c] {
			ret = append(ret, item)
			seen[c] = true
		}
	}
	for _, item := range sink {
		c := Channel{Sid: item.Sid, Ssid: item.Ssid}
		if !seen[c] {
			ret = append(ret, item)
			seen[c] = true
		}
	}
	return ret
}

func getRoomPopularityValueElem(channels map[PopularityKey]int) (elemMap map[PopularityKey]fts_ch_popular.ChannelPopularityElemInfo, err error) {
	req := fts_ch_popular.NewGetRoomPopularityValueReq()
	for k := range channels {
		req.Channels = append(req.Channels, &fts_ch_popular.Channel{
			Sid:  k.Sid,
			Ssid: k.Ssid,
			ID:   int64(k.ZoneID),
		})
	}
	now := time.Now()
	ret, err := chpopulardao.GetRoomPopularityElemValue(req)
	if err != nil || ret == nil || ret.Status != 0 {
		return nil, fmt.Errorf("GetRoomPopularityValue err=%+v, ret=%+v", err, ret)
	}
	yylog.Info("", zap.Int("len(channels)", len(channels)), zap.Any("timeElapsed", time.Since(now)))
	elemMap = make(map[PopularityKey]fts_ch_popular.ChannelPopularityElemInfo, len(ret.Values))
	for _, r := range ret.Values {
		if r == nil {
			continue
		}
		c := PopularityKey{Sid: r.Sid, Ssid: r.Ssid, ZoneID: int(r.ID)}
		elemMap[c] = *r
		yylog.Debug("", zap.Any("c", c), zap.Any("r", r))
	}
	return
}
