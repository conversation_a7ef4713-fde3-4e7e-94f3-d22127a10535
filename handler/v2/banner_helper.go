package v2

import (
	"context"
	"encoding/json"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
	"sort"
	"strings"

	"git.yy.com/server/jiaoyou/go_projects/api/common/constinfo"

	"git.yy.com/golang/gfy/v2/yy/yylog"
	"go.uber.org/zap"

	"git.yy.com/server/jiaoyou/go_projects/api/common/util"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/zhuiwan_channel"

	redisdao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/redisdao/v2"
)

/*
// bannerQuickEntryDeal 快速入口配置处理接口
type bannerQuickEntryDeal interface {
	quickEntryDeal(string, FilterKey) ([]QuickEntryInfo, error)
}

// BannerQuickEntryDealFunc .
type BannerQuickEntryDealFunc func(string, FilterKey) ([]QuickEntryInfo, error)

// QuickEntryDeal .
func (t BannerQuickEntryDealFunc) quickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return t(straceID, filterKey)
}
*/

// QuickEntryKey 快速入口配置的key
type QuickEntryKey struct {
	Business int64 // 业务类型
	Category int64 // 推荐类型
}

// FilterKey 客户端请求筛选key
type FilterKey struct {
	PageID   int32  // 页面类型	112--1:首页-游戏页/113--2:首页-娱乐页
	Platform int64  // 平台
	Market   string // 渠道
	Version  string // 版本
}

// QuickEntryInfo 快速入口配置数据
type QuickEntryInfo struct {
	ID                  int64  `json:"id"`                  // 唯一标识
	FunctionName        string `json:"functionName"`        // 功能名称
	FunctionRemark      string `json:"functionRemark"`      // 功能副标题
	FunctionPic         string `json:"functionPic"`         // 功能短图
	LongFunctionPic     string `json:"longFunctionPic"`     // 功能长图
	DarkFunctionPic     string `json:"darkFunctionPic"`     // 暗黑模式下的功能短图
	DarkLongFunctionPic string `json:"darkLongFunctionPic"` // 暗黑模式下的功能长图
	FunctionLink        string `json:"functionLink"`        // 跳转链接
	Weight              int32  `json:"-"`                   // 权重
	BackgroundPic1      string `json:"backgroundPic1"`      // 背景图片1
	BackgroundPic2      string `json:"backgroundPic2"`      // 背景图片2
	SVGA                string `json:"svga"`                // svga
	Pic                 string `json:"pic"`                 // 顶部内容图片
	DarkSVGA            string `json:"darkSvga"`            // 暗黑模式svga
	DarkPic             string `json:"darkPic"`             // 暗黑模式顶部内容图片
}

/*
var (
	onceDo                  sync.Once
	bannerQuickEntryDealMap map[QuickEntryKey]bannerQuickEntryDeal
)

var (
	errNotRegisterQuickEntry = errors.New("not register quickEntry")
)

func init() {
	// 追玩业务
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeZW,
		Category: categoryRecreationZW,
	}, BannerQuickEntryDealFunc(zwHomeAmusementQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeZW,
		Category: categoryGameZW,
	}, BannerQuickEntryDealFunc(zwHomeGameQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeZW,
		Category: categoryChatRoomZW,
	}, BannerQuickEntryDealFunc(zwHomeChatroomQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeZW,
		Category: categoryDiscoveryZW,
	}, BannerQuickEntryDealFunc(zwDiscoveryQuickEntryDeal))

	// Yomi业务
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYOMI,
		Category: categoryRecreationZW,
	}, BannerQuickEntryDealFunc(yomiHomeAmusementQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYOMI,
		Category: categoryGameZW,
	}, BannerQuickEntryDealFunc(yomiHomeGameQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYOMI,
		Category: categoryChatRoomZW,
	}, BannerQuickEntryDealFunc(yomiHomeChatroomQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYOMI,
		Category: categoryDiscoveryZW,
	}, BannerQuickEntryDealFunc(yomiDiscoveryQuickEntryDeal))

	// yaya业务
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYAYA,
		Category: categoryRecreationZW,
	}, BannerQuickEntryDealFunc(yayaHomeAmusementQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYAYA,
		Category: categoryGameZW,
	}, BannerQuickEntryDealFunc(yayaHomeGameQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYAYA,
		Category: categoryChatRoomZW,
	}, BannerQuickEntryDealFunc(yayaHomeChatroomQuickEntryDeal))
	RegisterQuickEntry(QuickEntryKey{
		Business: constinfo.BusinessTypeYAYA,
		Category: categoryDiscoveryZW,
	}, BannerQuickEntryDealFunc(yayaDiscoveryQuickEntryDeal))
}

// zwHomeGameQuickEntryDeal 追玩-首页-游戏
func zwHomeGameQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDeal(straceID, filterKey)
}

// zwHomeAmusementQuickEntryDeal 追玩-首页-娱乐
func zwHomeAmusementQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDeal(straceID, filterKey)
}

// zwHomeChatroomQuickEntryDeal 追玩-首页-聊天室
func zwHomeChatroomQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDeal(straceID, filterKey)
}

// zwDiscoveryQuickEntryDeal 追玩-发现页
func zwDiscoveryQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDeal(straceID, filterKey)
}

// yomiHomeGameQuickEntryDeal Yomi-首页-游戏
func yomiHomeGameQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYomi(straceID, filterKey)
}

// yomiHomeAmusementQuickEntryDeal Yomi-首页-娱乐
func yomiHomeAmusementQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYomi(straceID, filterKey)
}

// yomiHomeChatroomQuickEntryDeal Yomi-首页-聊天室
func yomiHomeChatroomQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYomi(straceID, filterKey)
}

// yomiDiscoveryQuickEntryDeal Yomi-发现页
func yomiDiscoveryQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYomi(straceID, filterKey)
}

// yayaHomeGameQuickEntryDeal YaYa-首页-游戏
func yayaHomeGameQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYaYa(straceID, filterKey)
}

// yayaHomeAmusementQuickEntryDeal YaYa-首页-娱乐
func yayaHomeAmusementQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYaYa(straceID, filterKey)
}

// yayaHomeChatroomQuickEntryDeal YaYa-首页-聊天室
func yayaHomeChatroomQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYaYa(straceID, filterKey)
}

// yayaDiscoveryQuickEntryDeal YaYa-发现页
func yayaDiscoveryQuickEntryDeal(straceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	return quickEntryDealYaYa(straceID, filterKey)
}

// quickEntryDeal 通用获取快速入口配置数据
func quickEntryDeal(traceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	data, err := redisdao.GetZWQuickEntryData()
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		return nil, errors.New("data empty")
	}

	var list []zhuiwan_channel.HomepageFunction
	if err = json.Unmarshal([]byte(data), &list); err != nil {
		return nil, err
	}

	if len(list) == 0 {
		return nil, errors.New("list empty")
	}

	res := make([]QuickEntryInfo, 0)
	for _, v := range list {
		// 页面ID
		if v.PageId != filterKey.PageID {
			continue
		}

		// 平台
		if v.Platform != int32(platformAndroidAndIOS) &&
			v.Platform != int32(filterKey.Platform) {
			yylog.Info("skip platform", zap.String("traceID", traceID), zap.Any("v", v))
			continue
		}

		// 渠道
		if v.MarketStatus == 1 || v.MarketStatus == 2 { // 0-全部(不用过滤)|1-部分显示(白名单)|2-部分不显示(黑名单)
			inTheList := false
			for _, validMarket := range v.ValidMarket {
				if filterKey.Market == validMarket {
					inTheList = true
				}
			}
			if (v.MarketStatus == 1 && !inTheList) || (v.MarketStatus == 2 && inTheList) {
				yylog.Info("skip market", zap.String("traceID", traceID), zap.Any("v", v))
				continue
			}
		}

		// 版本
		if v.VersionStatus == 1 || v.VersionStatus == 2 { // 1-部分显示(白名单), 2-部分不显示(黑名单)
			if len(v.ValidVersion) != 2 {
				continue
			}
			startVersion, endVersion := v.ValidVersion[0], v.ValidVersion[1]
			len1 := len(strings.Split(filterKey.Version, "."))
			len2 := len(strings.Split(startVersion, "."))
			len3 := len(strings.Split(endVersion, "."))
			if len1 != 3 || len2 != 3 || len3 != 3 {
				continue
			}
			isHitTarget := false
			if util.CompareVersion(filterKey.Version, startVersion) >= 0 && util.CompareVersion(filterKey.Version, endVersion) <= 0 {
				isHitTarget = true
			}
			if (v.VersionStatus == 1 && !isHitTarget) || (v.VersionStatus == 2 && isHitTarget) {
				yylog.Info("skip version", zap.String("traceID", traceID), zap.Any("v", v))
				continue
			}
		}

		if filterYunGame(v, filterKey) {
			continue
		}

		res = append(res, QuickEntryInfo{
			ID:                  v.ID,
			FunctionName:        v.FunctionName,
			FunctionRemark:      v.FunctionRemark,
			FunctionPic:         v.FunctionPic,
			LongFunctionPic:     v.LongFunctionPic,
			DarkFunctionPic:     v.DarkFunctionPic,
			DarkLongFunctionPic: v.DarkLongFunctionPic,
			FunctionLink:        v.FunctionLink,
			Weight:              v.Weight,
		})
	}

	// 按权重排序
	sort.Slice(res, func(i, j int) bool {
		return res[i].Weight > res[j].Weight
	})

	yylog.Info("", zap.String("traceID", traceID), zap.Int("res", len(res)))
	return res, nil
}

// 通用获取快速入口配置数据
func quickEntryDealYomi(traceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	data, err := redisdao.GetYomiQuickEntryData()
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		return nil, errors.New("data empty")
	}

	var list []zhuiwan_channel.HomepageFunction
	if err = json.Unmarshal([]byte(data), &list); err != nil {
		return nil, err
	}

	if len(list) == 0 {
		return nil, errors.New("list empty")
	}

	res := make([]QuickEntryInfo, 0)
	for _, v := range list {
		// 页面ID
		if v.PageId != filterKey.PageID {
			continue
		}

		// 平台
		if v.Platform != int32(platformAndroidAndIOS) &&
			v.Platform != int32(filterKey.Platform) {
			yylog.Info("skip platform", zap.String("traceID", traceID), zap.Any("v", v))
			continue
		}

		// 渠道
		if v.MarketStatus == 1 || v.MarketStatus == 2 { // 0-全部(不用过滤)|1-部分显示(白名单)|2-部分不显示(黑名单)
			inTheList := false
			for _, validMarket := range v.ValidMarket {
				if filterKey.Market == validMarket {
					inTheList = true
				}
			}
			if (v.MarketStatus == 1 && !inTheList) || (v.MarketStatus == 2 && inTheList) {
				yylog.Info("skip market", zap.String("traceID", traceID), zap.Any("v", v))
				continue
			}
		}

		// 版本
		if v.VersionStatus == 1 || v.VersionStatus == 2 { // 1-部分显示(白名单), 2-部分不显示(黑名单)
			if len(v.ValidVersion) != 2 {
				continue
			}
			startVersion, endVersion := v.ValidVersion[0], v.ValidVersion[1]
			len1 := len(strings.Split(filterKey.Version, "."))
			len2 := len(strings.Split(startVersion, "."))
			len3 := len(strings.Split(endVersion, "."))
			if len1 != 3 || len2 != 3 || len3 != 3 {
				continue
			}
			isHitTarget := false
			if util.CompareVersion(filterKey.Version, startVersion) >= 0 && util.CompareVersion(filterKey.Version, endVersion) <= 0 {
				isHitTarget = true
			}
			if (v.VersionStatus == 1 && !isHitTarget) || (v.VersionStatus == 2 && isHitTarget) {
				yylog.Info("skip version", zap.String("traceID", traceID), zap.Any("v", v))
				continue
			}
		}

		res = append(res, QuickEntryInfo{
			ID:                  v.ID,
			FunctionName:        v.FunctionName,
			FunctionRemark:      v.FunctionRemark,
			FunctionPic:         v.FunctionPic,
			LongFunctionPic:     v.LongFunctionPic,
			DarkFunctionPic:     v.DarkFunctionPic,
			DarkLongFunctionPic: v.DarkLongFunctionPic,
			FunctionLink:        v.FunctionLink,
			Weight:              v.Weight,
		})
	}

	// 按权重排序
	sort.Slice(res, func(i, j int) bool {
		return res[i].Weight > res[j].Weight
	})

	yylog.Info("", zap.String("traceID", traceID), zap.Int("res", len(res)))
	return res, nil
}

// 通用获取快速入口配置数据
func quickEntryDealYaYa(traceID string, filterKey FilterKey) ([]QuickEntryInfo, error) {
	data, err := redisdao.GetYaYaQuickEntryData()
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		return nil, errors.New("data empty")
	}

	var list []zhuiwan_channel.HomepageFunction
	if err = json.Unmarshal([]byte(data), &list); err != nil {
		return nil, err
	}

	if len(list) == 0 {
		return nil, errors.New("list empty")
	}

	res := make([]QuickEntryInfo, 0)
	for _, v := range list {
		// 页面ID
		if v.PageId != filterKey.PageID {
			continue
		}

		// 平台
		if v.Platform != int32(platformAndroidAndIOS) &&
			v.Platform != int32(filterKey.Platform) {
			yylog.Info("skip platform", zap.String("traceID", traceID), zap.Any("v", v))
			continue
		}

		// 渠道
		if v.MarketStatus == 1 || v.MarketStatus == 2 { // 0-全部(不用过滤)|1-部分显示(白名单)|2-部分不显示(黑名单)
			inTheList := false
			for _, validMarket := range v.ValidMarket {
				if filterKey.Market == validMarket {
					inTheList = true
				}
			}
			if (v.MarketStatus == 1 && !inTheList) || (v.MarketStatus == 2 && inTheList) {
				yylog.Info("skip market", zap.String("traceID", traceID), zap.Any("v", v))
				continue
			}
		}

		// 版本
		if v.VersionStatus == 1 || v.VersionStatus == 2 { // 1-部分显示(白名单), 2-部分不显示(黑名单)
			if len(v.ValidVersion) != 2 {
				continue
			}
			startVersion, endVersion := v.ValidVersion[0], v.ValidVersion[1]
			len1 := len(strings.Split(filterKey.Version, "."))
			len2 := len(strings.Split(startVersion, "."))
			len3 := len(strings.Split(endVersion, "."))
			if len1 != 3 || len2 != 3 || len3 != 3 {
				continue
			}
			isHitTarget := false
			if util.CompareVersion(filterKey.Version, startVersion) >= 0 && util.CompareVersion(filterKey.Version, endVersion) <= 0 {
				isHitTarget = true
			}
			if (v.VersionStatus == 1 && !isHitTarget) || (v.VersionStatus == 2 && isHitTarget) {
				yylog.Info("skip version", zap.String("traceID", traceID), zap.Any("v", v))
				continue
			}
		}

		res = append(res, QuickEntryInfo{
			ID:                  v.ID,
			FunctionName:        v.FunctionName,
			FunctionRemark:      v.FunctionRemark,
			FunctionPic:         v.FunctionPic,
			LongFunctionPic:     v.LongFunctionPic,
			DarkFunctionPic:     v.DarkFunctionPic,
			DarkLongFunctionPic: v.DarkLongFunctionPic,
			FunctionLink:        v.FunctionLink,
			Weight:              v.Weight,
		})
	}

	// 按权重排序
	sort.Slice(res, func(i, j int) bool {
		return res[i].Weight > res[j].Weight
	})

	yylog.Info("", zap.String("traceID", traceID), zap.Int("res", len(res)))
	return res, nil
}

// RegisterQuickEntry 注册快速入口配置的页面处理
func RegisterQuickEntry(quickEntryKey QuickEntryKey, fn bannerQuickEntryDeal) {
	if bannerQuickEntryDealMap == nil {
		onceDo.Do(func() {
			bannerQuickEntryDealMap = make(map[QuickEntryKey]bannerQuickEntryDeal)
		})
	}
	if _, ok := bannerQuickEntryDealMap[quickEntryKey]; ok {
		return
	}
	bannerQuickEntryDealMap[quickEntryKey] = fn
}

// GetQuickEntryList 获取快速入口配置数据
func GetQuickEntryList(straceID string, quickEntryKey QuickEntryKey, filterKey FilterKey) (list []QuickEntryInfo, err error) {
	if _, ok := bannerQuickEntryDealMap[quickEntryKey]; !ok {
		yylog.Warn("can not register quick entry", zap.Any("key", quickEntryKey), zap.Any("filter key", filterKey))
		return nil, errNotRegisterQuickEntry
	}

	fn := bannerQuickEntryDealMap[quickEntryKey]
	list, err = fn.quickEntryDeal(straceID, filterKey)
	yylog.Info("get quick entry list", zap.Any("key", quickEntryKey), zap.Any("filter key", filterKey),
		zap.Int("list size", len(list)))
	return
}
*/

func getQuickEntryData(key QuickEntryKey) (list []zhuiwan_channel.HomepageFunction, err error) {
	var data string
	switch key.Business {
	case constinfo.BusinessTypeZW:
		data, err = redisdao.GetZWQuickEntryData()
	case constinfo.BusinessTypeYOMI:
		data, err = redisdao.GetYomiQuickEntryData()
	case constinfo.BusinessTypeYAYA:
		data, err = redisdao.GetYaYaQuickEntryData()
	default:
		return []zhuiwan_channel.HomepageFunction{}, nil
	}

	if err = json.Unmarshal([]byte(data), &list); err != nil || len(list) == 0 {
		return nil, err
	}

	return
}

// 顶部功能区统一入口
func quickEntryDealV2(ctx context.Context, key QuickEntryKey, filterKey FilterKey) ([]QuickEntryInfo, error) {
	yylog.Info("", fts.TraceID(ctx), zap.Any("key", key), zap.Any("filterKey", filterKey))

	list, err := getQuickEntryData(key)
	if err != nil || len(list) == 0 {
		return nil, err
	}

	res := make([]QuickEntryInfo, 0)
	for _, v := range list {
		// 页面ID
		if v.PageId != filterKey.PageID {
			continue
		}

		// 平台
		if v.Platform != int32(platformAndroidAndIOS) &&
			v.Platform != int32(filterKey.Platform) {
			yylog.Info("skip platform", fts.TraceID(ctx), zap.Any("v", v))
			continue
		}

		// 渠道
		if v.MarketStatus == 1 || v.MarketStatus == 2 { // 0-全部(不用过滤)|1-部分显示(白名单)|2-部分不显示(黑名单)
			inTheList := false
			for _, validMarket := range v.ValidMarket {
				if filterKey.Market == validMarket {
					inTheList = true
				}
			}
			if (v.MarketStatus == 1 && !inTheList) || (v.MarketStatus == 2 && inTheList) {
				yylog.Info("skip market", fts.TraceID(ctx), zap.Any("v", v))
				continue
			}
		}

		// 版本
		if v.VersionStatus == 1 || v.VersionStatus == 2 { // 1-部分显示(白名单), 2-部分不显示(黑名单)
			if len(v.ValidVersion) != 2 {
				continue
			}
			startVersion, endVersion := v.ValidVersion[0], v.ValidVersion[1]
			len1 := len(strings.Split(filterKey.Version, "."))
			len2 := len(strings.Split(startVersion, "."))
			len3 := len(strings.Split(endVersion, "."))
			if len1 != 3 || len2 != 3 || len3 != 3 {
				continue
			}
			isHitTarget := false
			if util.CompareVersion(filterKey.Version, startVersion) >= 0 && util.CompareVersion(filterKey.Version, endVersion) <= 0 {
				isHitTarget = true
			}
			if (v.VersionStatus == 1 && !isHitTarget) || (v.VersionStatus == 2 && isHitTarget) {
				yylog.Info("skip version", fts.TraceID(ctx), zap.Any("v", v))
				continue
			}
		}

		// android开启送审, 快接入口没有云游戏
		if key.Business == constinfo.BusinessTypeZW && filterYunGame(v, filterKey) {
			yylog.Info(
				"skip android yun game",
				fts.TraceID(ctx),
				zap.String("market", filterKey.Market),
				zap.String("version", filterKey.Version),
				zap.Any("v", v))
			continue
		}

		res = append(res, QuickEntryInfo{
			ID:                  v.ID,
			FunctionName:        v.FunctionName,
			FunctionRemark:      v.FunctionRemark,
			FunctionPic:         v.FunctionPic,
			LongFunctionPic:     v.LongFunctionPic,
			DarkFunctionPic:     v.DarkFunctionPic,
			DarkLongFunctionPic: v.DarkLongFunctionPic,
			FunctionLink:        v.FunctionLink,
			Weight:              v.Weight,
			SVGA:                v.QuickSvga,
			Pic:                 v.QuickPic,
			DarkSVGA:            v.DarkQuickSvga,
			DarkPic:             v.DarkQuickPic,
		})
	}

	// 按权重排序
	sort.Slice(res, func(i, j int) bool {
		return res[i].Weight > res[j].Weight
	})

	yylog.Info("", fts.TraceID(ctx), zap.Int("res", len(res)))
	return res, nil
}

func filterYunGame(v zhuiwan_channel.HomepageFunction, filterKey FilterKey) bool {
	// android开启送审, 快接入口没有云游戏
	if filterKey.Platform == platformAndroid && strings.Contains(v.FunctionName, "云游戏") {
		if isFilter, _ := filterAppReviewAndroid(filterKey.Market, filterKey.Version); isFilter {
			return true
		}
	}
	return false
}

func getQuickEntryKey(business, category int64) QuickEntryKey {
	return QuickEntryKey{
		Business: business,
		Category: category,
	}
}

func getFilterKey(platform Platform, category int64, market, version string) FilterKey {
	return FilterKey{
		PageID:   int32(category),
		Platform: int64(platform),
		Market:   market,
		Version:  version,
	}
}

// filterAppReviewAndroid android开启送审, 快接入口没有云游戏
func filterAppReviewAndroid(market, version string) (isFilter bool, err error) {
	data, err := redisdao.GetZWAppReviewData()
	if err != nil {
		yylog.Error("GetZWAppReviewData", zap.Error(err))
		return
	}

	yylog.Debug("", zap.String("market", market), zap.String("version", version), zap.String("data", data))

	if len(data) == 0 {
		return
	}

	var list []*zhuiwan_channel.ReviewMsg
	if err = json.Unmarshal([]byte(data), &list); err != nil {
		yylog.Error("unmarshal", zap.Error(err))
		return
	}

	for _, v := range list {
		if market == v.AppMarket && util.CompareVersion(version, v.AppVersion) >= 0 {
			yylog.Info("android filter app review", zap.String("market", market), zap.String("version", version),
				zap.Any("v", v))
			isFilter = true
			return
		}
	}

	return
}
