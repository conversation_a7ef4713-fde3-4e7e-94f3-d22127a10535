package v2

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	mgodao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/mgodao/v2"

	"git.yy.com/server/jiaoyou/go_projects/api/common/constinfo"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
)

const (
	topListTabID      = 11400   // 热门
	partyListTabID    = 10500   // 交友派对
	multiVideoTabID   = 10400   // 多人比拼
	wonderfulLive     = 10600   // 精彩直播
	superCompereTabID = 4011400 // 超级主持
)

const (
	topListRegularName = "交友-顶部推荐名单"
)

// terminal-终端
const (
	zwTerminal     = 1 // 追玩APP(现在叫做 yo交友)
	pcGameTerminal = 2 // PC导流专区
	yoTerminal     = 3 // Yo语音
	yoGameTerminal = 4 // Yo开黑
	yayaTerminal   = 5 // yaya
)

// platform-平台 (terminal=2时只能选4, terminal=1时只能选1-3)
const (
	platformAndroid       = 1
	platformIOS           = 2
	platformAndroidAndIOS = 3
	platformPC            = 4
	platformPCWeb         = 5
	platformPCAndAPP      = 6
)

// business-业务类型
// 新增枚举后需要检查函数： isZhuiWanChannel
const (
	businessNone      = 0
	businessJY        = 1   // 交友
	businessPK        = 2   // 约战
	businessBaby      = 3   // 宝贝
	businessBasic     = 4   // 打通房
	businessPW        = 101 // 派单房
	businessGame      = 201 // 游戏房
	businessVoiceRoom = 301 // 语音房
	businessGameTeam  = 401 // YY开黑房
	businessSkillCard = 501 // 技能卡
)

// 按照业务取在线频道时,后面的业务会覆盖前面的
var businessList = [...]int{
	businessJY,
	businessPK,
	businessBaby,
	businessBasic,
	businessPW,
	businessGame,
	businessVoiceRoom,
	businessGameTeam,
	businessSkillCard,
}

// category枚举： (我们自己业务用)
// 每个值对应导航管理页面的一个tabID
// 变动后需要修改函数： isValidPage; getPageName
const (
	// ---- 追玩APP ----
	categoryRecreation       = 1  // 追玩-娱乐页面
	categoryGame             = 2  // 追玩-游戏页面
	categoryDiscovery        = 3  // 追玩-发现页
	categoryFeed             = 4  // 追玩-分流推荐
	categoryCloudGame        = 5  // 追玩-云游戏
	categoryGameRegion       = 6  // 追玩-游戏专区
	categoryChatRoom         = 7  // 追玩-聊天室
	categoryDynamicLanding   = 8  // 追玩-动态落地页
	categorySidebar          = 9  // 追玩-房间内侧边栏
	categoryH5VoiceRoom      = 10 // H5语音房推荐
	categoryPopularRecommend = 11 // 人气推荐
	categoryInsertModule     = 12 // 插入模块
	categoryYoGameCenter     = 13 // 游戏专区
	// 新增游戏页面, 没配置导航页面, 故固定此id, 用于映射追玩后台, 对接客户端
	categoryQuickGame = 100 // 追玩-快速入口-游戏

	// ---- PC导流专区 ----
	pcGameCategoryHome   = 1 // 首页
	pcGameCategoryDating = 2 // 交友互动
	pcGameCategoryTeam   = 3 // 开黑陪玩
)

// 这些常量是追玩业务用的
const (
	categoryGameZW       = 1 // 首页-游戏
	categoryRecreationZW = 2 // 首页-娱乐
	categoryChatRoomZW   = 5 // 首页-聊天室
	categoryDiscoveryZW  = 3 // 首页-发现页
	categoryQuickGameZW  = 6 // 快速入口-游戏
)

// 房间分类
const (
	// 语音房模板
	roomClassParty    = 2001 // 派对
	roomClassChatRoom = 2002 // 扩列
	roomClassMiniGame = 2003 // 小游戏
	roomClassGame     = 2004 // 游戏

	// 基础模板
	roomClassReception = 1001 // 开黑接待
	roomClassBasicGame = 1002 // 游戏

	roomClassSkillCard = 1003 // 技能卡

	roomClassJY   = 3001 // 交友
	roomClassPK   = 5001 // 约战
	roomClassBaby = 6001 // 宝贝
)

// 房间类型
const (
	// 语音房模板
	roomTypeVoiceTemplateParty      = 1  // 派对
	roomTypeVoiceTemplateChatRoom   = 2  // 扩列
	roomTypeVoiceTemplateBilliards  = 3  // 桌球
	roomTypeVoiceTemplateUndercover = 4  // 谁是卧底
	roomTypeVoiceTemplateWZRY       = 5  // 王者荣耀
	roomTypeVoiceTemplateHPJY       = 6  // 和平精英
	roomTypeVoiceTemplateLOL        = 7  // LOL手游
	roomTypeVoiceTemplateDWRG       = 8  // 第五人格
	roomTypeVoiceTemplateSMZH       = 9  // 使命召唤手游
	roomTypeVoiceTemplateCF         = 10 // CF手游
	roomTypeVoiceTemplateJCCZZ      = 11 // 金铲铲之战
	roomTypeVoiceTemplateHLBT       = 12 // 哈利波特
	// 基础模板
	roomTypeBasicTemplateReception = 13 // 开黑接待
	roomTypeBasicTemplateWZRY      = 14 // 王者荣耀
	roomTypeBasicTemplateHPJY      = 15 // 和平精英
	roomTypeBasicTemplateLOL       = 16 // LOL
	roomTypeBasicTemplateMSSJ      = 17 // 魔兽世界
	roomTypeBasicTemplateJWS       = 18 // 剑网3
	roomTypeBasicTemplateCYHX      = 19 // 穿越火线
	roomTypeBasicTemplateDNF       = 20 // DNF
	// 交友模板
	roomTypeJYTemplateDating      = 21 // 相亲交友
	roomTypeJYTemplateVideoDating = 22 // 多人
	// 约战模板
	roomTypePKTemplate = 23 // 约战PK
	// 宝贝模板
	roomTypeBabyTemplate = 24 // 游戏宝贝

	roomTypeSkillCardTemplate = 29 // 技能卡
)

// viewType 推荐的类型
type viewType int

const (
	viewTypeNone viewType = 0 // 未定义
	viewTypePos  viewType = 1 // 按位置的推荐
	viewTypeAuto viewType = 2 // 没有指定位置，自动
	viewTypeSink viewType = 3 // 沉底的
)

// 推荐进入方式
const (
	enterTypeInitiative = 1 // 主动
	enterTypeSupplement = 2 // 补量白名单
)

// Action boss后台操作
var Action = "action"

const (
	// Unknow ...
	Unknow = iota
	// BoosActionCreate 添加
	BoosActionCreate
	// BoosActionUpdate 更新
	BoosActionUpdate
	// BoosActionRead 读取
	BoosActionRead
	// BoosActionDelete 删除
	BoosActionDelete
)

type sortType int

const (
	sortByWeight sortType = 0
	sortByPsu    sortType = 1
)

var allTerminal = []int{
	zwTerminal,
	pcGameTerminal,
	yoTerminal,
	yoGameTerminal,
	yayaTerminal}

// BoosAction boss后台操作
func BoosAction(action int) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(Action, action)
		c.Next()
	}
}

// 返回给客户端的推荐tab信息
type recommendTab struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`               // 名称
	Weight   int    `json:"weight"`             // 权重
	Style    int    `bson:"style" json:"style"` // 0：样式1、1：样式2、2：样式3、3：样式4
	RoomType []int  `bson:"style" json:"-"`
}

type (
	// 返回给客户端的推荐信息
	viewItem struct {
		UID                  int64          `json:"uid"`   // 主持uid
		Sid                  int64          `json:"sid"`   // 频道
		Ssid                 int64          `json:"ssid"`  // 子频道
		Asid                 int64          `json:"asid"`  // 短位频道
		Psu                  int64          `json:"psu"`   // 人气值
		Title                string         `json:"title"` // 标题
		Nick                 string         `json:"nick"`  // 主持昵称
		Cover                string         `json:"cover"` // 推荐图
		ZoneID               int64          `json:"zoneId"`
		ConfZoneID           int64          `json:"confZoneID"` // 原始配置的zoneID
		Label                string         `json:"label"`      // 标签
		Attributes           string         `json:"attributes"`
		AttributesIcon       string         `json:"attributesIcon"` // 右上角内容标签（Yo1.3开始使用，原有的attributes、bigIcon废弃）
		TemplateID           int64          `json:"templateId"`     // 模板id
		Weight               int64          `json:"weight"`
		Mode                 int            `json:"mode"` // 推荐类型：1-按主持 2-按频道
		Sex                  int            `json:"sex"`  // 0：女、1: 男
		Position             int64          `json:"position"`
		Business             int            `json:"business"` // 1:交友、2：约战、3：宝贝、4：打通房、101：派单房、201：游戏房、301：语音房
		Platform             int            `json:"platform"`
		GameType             int            `json:"gameType"` // 0: 相亲、2:团战 、7:乱斗 、8:多人视频、18:多人pk、19:多人乱斗
		LabelExpired         bool           `json:"-"`
		ShowAttributesStatus bool           `json:"-"`
		Icon                 string         `json:"-"`
		BigIcon              string         `json:"bigIcon"`
		MatchStatus          string         `json:"matchStatus"` // 匹配状态 (仅开黑车队玩法用)
		SubTitle             string         `json:"subTitle"`    // 副标题
		ViewType             viewType       `json:"viewType"`    // 板块类型： 1--位置   2--自动   3--沉底
		GameID               int            `json:"gameId"`
		GameName             string         `json:"gameName"`
		RoomTag              int32          `json:"roomTag"`
		Lock                 bool           `json:"lock"`       // 房间是否上锁
		Background           string         `json:"background"` // 背景图
		LiveInfo             string         `json:"liveInfo"`   // 流信息 base64编码
		GuestUID             []int64        `json:"guestUid"`   // 多人视频、多人pk、多人乱斗：嘉宾uid
		Match                match          `json:"match"`      // 乱斗、多人视频乱斗玩法对方信息
		OnlineInfo           []guestInfo    `json:"onlineInfo"` // 频道内观众列表 (目前仅追玩有)
		EnterType            int64          `json:"enterType"`  // 进入方式 1-主动进入 2-白名单补量
		RoomType             int            `json:"roomType"`   // 房间类型
		RoomClass            int            `json:"roomClass"`  // 房间分类
		Terminal             []int          `json:"terminal"`
		MixStream            string         `json:"mixStream"` // json字符串，开播混画流
		RecommendManageID    string         `json:"recommendManageId"`
		RoomName             string         `json:"roomName"`             // 房间名称
		RoomCover            string         `json:"roomCover"`            // 房间封面
		AttributesStatus     string         `json:"attributesStatus"`     // 玩法状态(红包>[团战|PK] 一般是列表展示时候使用)
		RcAttributesStatus   string         `json:"rcAttributesStatus"`   // 玩法状态(红包>[团战|PK] 一般是豆腐块样式展示时候使用)
		SideAttributesStatus string         `json:"sideAttributesStatus"` // 侧边栏玩法状态
		Avatar               string         `json:"avatar"`               // 主持头像
		CardStyle            int            `json:"cardStyle"`            // 展示样式 0-默认 1-开黑车队
		RoomNo               int64          `json:"roomNo"`               // 房间号
		SubRoomClass         int            `json:"subRoomClass"`
		BackgroundNew        string         `json:"backgroundNew"` // 新背景图
		BdCover              string         `json:"bdCover"`
		BdTitle              string         `json:"bdTitle"`
		LayoutConfig         string         `json:"layoutConfig"`      // 语音房布局信息 (game_type,video_mode,room_op_type,sub_game_type)
		RecommendBgImgUrl    string         `json:"recommendBgImgUrl"` // UGC 推荐豆腐块显示样式背景图
		GameTypeIcon         string         `json:"gameTypeIcon"`      // UGC 一级玩法类型图标
		SubGameTypeIcon      string         `json:"subGameTypeIcon"`   // UGC 二级玩法类型图标
		StatusText           string         `json:"statusText"`        // UGC 状态标识文案，如 组队中、等待中、闲聊中等
		OnlineCnt            int32          `json:"onlineCnt"`         // UGC 在线人数，新增字段
		ExtendAttrList       []extendAttrVo `json:"extendAttrList"`    // UGC 扩展属性信息，对应 extendAttrVo 类型
	}

	// 透传属性
	extendAttrVo struct {
		Icon    string `json:"icon"`    // 图标
		Title   string `json:"title"`   // 标题，可能为空，空的话就只需要显示 content
		Content string `json:"content"` // 内容
	}

	// 在线信息
	onlineChannelInfo struct {
		Sid                  int64       `json:"sid"`        // 顶级频道
		Ssid                 int64       `json:"ssid"`       // 子频道
		UID                  int64       `json:"uid"`        // 主持uid
		Business             int         `json:"business"`   // 业务类型
		GameType             int         `json:"gameType"`   // 当前玩法类型
		Icon                 string      `json:"icon"`       // 追玩 列表标签 来源于接口：skillcard -> RecDataServiceImpl#batchQueryData zhuiya.zy_game_recommend.icon 字段
		BigIcon              string      `json:"bigIcon"`    // 追玩 卡片标签 来源于接口：skillcard -> RecDataServiceImpl#batchQueryData zhuiya.zy_game_recommend.big_icon 字段
		PlayType             int         `json:"playType"`   // 1-开黑组队、2-扩列聊天
		GameID               int         `json:"gameId"`     // 云游戏gameId
		GameName             string      `json:"gameName"`   // 游戏名称
		OnlineList           []guestInfo `json:"onlineList"` // 频道内观众列表 (目前只有追玩数据,最多返回10个)
		VsSid                int64       `json:"vsSid"`      // 乱斗、多人乱斗对方频道
		VsSsid               int64       `json:"vsSsid"`     // 乱斗、多人乱斗对方频道
		VsUID                int64       `json:"vsUid"`      // 乱斗、多人乱斗对方主持uid
		Guest                []int64     `json:"guest"`      // 乱斗、多人乱斗嘉宾uid
		RoomType             int         `json:"roomType"`   // 房间类型
		RoomClass            int         `json:"roomClass"`  // 房间分类
		Terminal             []int       `json:"terminal"`
		Nick                 string      `json:"nick"`
		RoomName             string      `json:"roomName"`             // 房间名称
		RoomCover            string      `json:"roomCover"`            // 房间封面
		AttributesStatus     string      `json:"attributesStatus"`     // 玩法状态
		RcAttributesStatus   string      `json:"rcAttributesStatus"`   // 玩法状态 豆腐块样式下，右上角图标地址：Yo语音APP-人气房间，Yo语音官网-语聊大厅
		BdAttributesStatus   string      `json:"bdAttributesStatus"`   // 玩法状态 百度&贴吧 豆腐块样式下，右上角图标地址：贴吧/好看-热门&业务tab
		SideAttributesStatus string      `json:"sideAttributesStatus"` // 侧边栏玩法状态
		RoomNo               int64       `json:"roomNo"`               // 房间号
		SubRoomClass         int         `json:"subRoomClass"`
		LayoutConfig         string      `json:"layoutConfig"` // 语音房布局信息 (game_type,video_mode,room_op_type)
	}

	// 观众信息
	guestInfo struct {
		UID       int64  `json:"uid"`
		Avatar    string `json:"avatar"`
		Nick      string `json:"nick"`
		Gender    int32  `json:"gender"`
		Seat      int32  `json:"seat"`
		Avatar100 string `json:"avatar100"`
		Avatar60  string `json:"avatar60"`
		AvatarHD  string `json:"avatarHd"`
	}

	// 乱斗、多人乱斗对方频道信息
	match struct {
		UID      int64   `json:"uid"`      // 对方主持uid
		Sid      int64   `json:"sid"`      // 对方频道
		Ssid     int64   `json:"ssid"`     // 对方子频道
		GuestUID []int64 `json:"guestUid"` // 多人pk、多人乱斗：对方嘉宾uid
	}
)

func (v *viewItem) String() string {
	return fmt.Sprintf("{uid=%d, sid=%d, ssid=%d, asid=%d, zonedId=%d, position=%d, weight=%d, psu=%d, title=%s, "+
		"cover=%s, label=%s, attributes=%s, bigIcon=%s, templateId=%d, sex=%d, business=%d, platform=%d, "+
		"gameType=%d, labelExpired=%t, showAttributesStatus=%t, mode=%d, viewType=%d, gameId=%d, gameName=%s, "+
		"guestUid=%+v, match=%+v, roomType=%d, roomClass=%d, attributesIcon=%s, terminal=%v,"+
		" recommendManageId=%s, roomName=%s, attributesStatus=%s, avatar=%s, roomNo=%d, background=%s, backgroundNew=%s"+
		" bdCover=%s, bdTitle=%s }",
		v.UID, v.Sid, v.Ssid, v.Asid, v.ZoneID, v.Position, v.Weight, v.Psu, v.Title, v.Cover,
		v.Label, v.Attributes, v.BigIcon, v.TemplateID, v.Sex, v.Business, v.Platform,
		v.GameType, v.LabelExpired, v.ShowAttributesStatus, v.Mode, v.ViewType, v.GameID,
		v.GameName, v.GuestUID, v.Match, v.RoomType, v.RoomClass, v.AttributesIcon,
		v.Terminal, v.RecommendManageID, v.RoomName, v.AttributesStatus, v.Avatar, v.RoomNo,
		v.Background, v.BackgroundNew, v.BdCover, v.BdTitle)
}

func (v *onlineChannelInfo) String() string {
	return fmt.Sprintf("{uid=%d, sid=%d, ssid=%d, business=%d, gameType=%v, icon=%s, bigIcon=%s, playType=%d, "+
		"gameId=%d, vsSid=%d, vsSsid=%d, vsUid=%d, guest=%+v, onlineList=%+v, roomType=%d, roomClass=%d, terminal=%d"+
		"roomName=%s, attributesStatus=%s, roomNo=%d}",
		v.UID, v.Sid, v.Ssid, v.Business, v.GameType, v.Icon, v.BigIcon, v.PlayType, v.GameID,
		v.VsSid, v.VsSsid, v.VsUID, v.Guest, v.OnlineList, v.RoomType, v.RoomClass, v.Terminal,
		v.RoomName, v.AttributesStatus, v.RoomNo)
}

func (m *match) String() string {
	return fmt.Sprintf("{uid=%d, sid=%d, ssid=%d, guest=%+v}", m.UID, m.Sid, m.Ssid, m.GuestUID)
}

// ChPopularInfo 人气信息
type ChPopularInfo struct {
	Page              string                  `json:"page"`
	Tab               string                  `json:"tab"`
	ZoneID            int64                   `json:"zoneId"`
	ZoneName          string                  `json:"zoneName"`
	Position          int64                   `json:"position"`
	OnlineUser        int64                   `json:"onlineUser"` //
	PublicScreenSpeak int64                   `json:"publicScreenSpeak"`
	MicorderCnt       int64                   `json:"micorderCnt"` //
	PropsFlow         int64                   `json:"propsFlow"`   // 礼物流水
	LiveDuration      int64                   `json:"liveDuration"`
	RobotCount        int64                   `json:"robotCount"`  // 机器人在线
	SeatAreaNum       int64                   `json:"seatAreaNum"` // 座位区人数
	Popularity        int64                   `json:"popularity"`
	Formula           mgodao.FormulaParameter `json:"formula"`
	Platform          int                     `json:"platform"`
	ActivePopularity  int64                   `json:"activePopularity"` // 活跃人气值
	PropsPopularity   int64                   `json:"propsPopularity"`  // 营收人气值
}

// ChPopularDescribe 返回给客户端的推荐信息
type ChPopularDescribe struct {
	UID               int64           `json:"uid"`
	Sid               int64           `json:"sid"`
	Ssid              int64           `json:"ssid"`
	Terminal          int             `json:"terminal"`
	Title             string          `json:"title"`
	ChPopularInfoList []ChPopularInfo `json:"chPopularInfoList"`
}

// ChInfoKey 人气值返回key
type ChInfoKey struct {
	UID      int64 `json:"uid"`
	Sid      int64 `json:"sid"`
	Ssid     int64 `json:"ssid"`
	Terminal int   `json:"terminal"`
}

// PopularityKey 人气值key
type PopularityKey struct {
	Sid    int64 `json:"sid"`
	Ssid   int64 `json:"ssid"`
	ZoneID int   `json:"ZoneID"`
}

type notRecommendItem struct {
	UID    int64  `json:"uid"`
	Sid    int64  `json:"sid"`
	Ssid   int64  `json:"ssid"`
	ZoneID int64  `json:"zoneId"`
	Reason string `json:"reason"`
}

func isRecommendTerminal(appTerminal int, recommendTerminal []int) bool {
	if len(recommendTerminal) == 0 {
		return true
	}
	for _, terminal := range recommendTerminal {
		if terminal == appTerminal {
			return true
		}
	}
	return false
}

func isAPPTerminal(terminal int) bool {
	return terminal == zwTerminal || terminal == yoTerminal || terminal == yoGameTerminal || terminal == yayaTerminal
}

func isYoTerminal(terminal int) bool {
	return terminal == yoTerminal || terminal == yoGameTerminal
}

func isZWTerminal(terminal int) bool {
	return terminal == zwTerminal
}

func getImageURL(terminal int, url string) string {
	if isYoTerminal(terminal) {
		url = getYoImageURL(url)
	}
	return strings.Replace(url, "http:", "https:", 1)
}

func getYoImageURL(url string) string {
	if !strings.Contains(url, "makefriends") {
		return url
	}
	n := strings.LastIndex(url, "/")
	if n == -1 {
		return url
	}
	return "https://yomipic1.yy.com" + url[n:]
}

func isValidCategory(category int, expect ...int) bool {
	for _, c := range expect {
		if category == c {
			return true
		}
	}
	return false
}

// 检查终端和分类是否有效
func isValidPage(terminal, category int) bool {
	switch terminal {
	case zwTerminal, yoTerminal, yoGameTerminal:
		return isValidCategory(category,
			categoryRecreation,
			categoryGame,
			categoryDiscovery,
			categoryFeed,
			categoryCloudGame,
			categoryGameRegion,
			categoryChatRoom,
			categoryDynamicLanding,
			categorySidebar,
			categoryH5VoiceRoom,
			categoryPopularRecommend,
			categoryInsertModule,
			categoryYoGameCenter,
		)
	case pcGameTerminal:
		return isValidCategory(category,
			pcGameCategoryHome,
			pcGameCategoryDating,
			pcGameCategoryTeam)
	case yayaTerminal:
		return isValidCategory(category,
			categoryRecreation,
			categoryGame,
			categorySidebar,
			categoryPopularRecommend)
	default:
	}
	return false
}

func expected(t int, all ...int) bool {
	if len(all) == 0 {
		return true
	}
	for _, it := range all {
		if t == it {
			return true
		}
	}
	return false
}

func isRecommendGameType(t int, all []int) bool {
	return expected(t, all...)
}

func isValidRecommendGameType(gameType []int) bool {
	for _, t := range gameType {
		if !isRecommendGameType(t, []int{
			constinfo.KOrdinary,
			constinfo.KVideoDating,
			constinfo.KChannelFight,
			constinfo.KTeamFight,
			constinfo.KBirthday,
			constinfo.KVideoPk,
			constinfo.KVideoFightMatch,
			businessPW,
			businessGame,
			businessVoiceRoom,
		}) {
			return false
		}
	}
	return true
}

func gameType2RoomType(gameType int) int {
	if gameType == constinfo.KVideoDating || gameType == constinfo.KVideoPk || gameType == constinfo.KVideoFightMatch {
		return roomTypeJYTemplateVideoDating
	}
	return roomTypeJYTemplateDating
}

func isRecommendRoomType(t int, all []int) bool {
	return expected(t, all...)
}

func isValidRecommendRoomType(roomType []int) bool {
	for _, gt := range roomType {
		if !isRecommendRoomType(gt, []int{
			roomTypeVoiceTemplateParty,
			roomTypeVoiceTemplateChatRoom,
			roomTypeVoiceTemplateBilliards,
			roomTypeVoiceTemplateUndercover,
			roomTypeVoiceTemplateWZRY,
			roomTypeVoiceTemplateHPJY,
			roomTypeVoiceTemplateLOL,
			roomTypeVoiceTemplateDWRG,
			roomTypeVoiceTemplateSMZH,
			roomTypeVoiceTemplateCF,
			roomTypeVoiceTemplateJCCZZ,
			roomTypeVoiceTemplateHLBT,
			roomTypeBasicTemplateReception,
			roomTypeBasicTemplateWZRY,
			roomTypeBasicTemplateHPJY,
			roomTypeBasicTemplateLOL,
			roomTypeBasicTemplateMSSJ,
			roomTypeBasicTemplateJWS,
			roomTypeBasicTemplateCYHX,
			roomTypeBasicTemplateDNF,
			roomTypeJYTemplateDating,
			roomTypeJYTemplateVideoDating,
			roomTypePKTemplate,
			roomTypeBabyTemplate,
			roomTypeSkillCardTemplate,
		}) {
			return false
		}
	}
	return true
}

// 判断platform与terminal是否冲突
func isValidPlatform(terminal, platform int) bool {
	switch terminal {
	case zwTerminal, yoTerminal, yoGameTerminal, yayaTerminal:
		return platform == platformAndroid ||
			platform == platformIOS ||
			platform == platformAndroidAndIOS ||
			platform == platformPCAndAPP
	case pcGameTerminal:
		return platform == platformPC
	default:
	}
	return false
}

func getBusinessChineseName(business int) string {
	var r string
	switch business {
	case businessJY:
		r = "交友"
	case businessPK:
		r = "约战"
	case businessBaby:
		r = "宝贝"
	case businessPW:
		r = "派单房"
	case businessGame:
		r = "游戏房"
	case businessBasic:
		r = "打通房"
	case businessVoiceRoom:
		r = "语音房"
	case businessGameTeam:
		r = "YY开黑房"
	case businessSkillCard:
		r = "技能卡"
	default:
		r = strconv.FormatInt(int64(business), 10)
	}
	return r
}

// 判断业务类型是否有效
func isValidBusiness(business int) bool {
	for _, b := range []int{
		businessJY,
		businessPK,
		businessBaby,
		businessBasic,
		businessPW,
		businessGame,
		businessVoiceRoom,
		businessGameTeam,
		businessSkillCard,
	} {
		if b == business {
			return true
		}
	}
	return false
}

func getAllBusiness() []int {
	return []int{
		businessJY,
		businessPK,
		businessBaby,
		businessBasic,
		businessPW,
		businessGame,
		businessVoiceRoom,
	}
}

func getTemplateID(business int) int64 {
	m := map[int]int64{
		businessJY:        268435460,
		businessPK:        33554522,
		businessBaby:      268435466,
		businessPW:        33554530,
		businessGame:      33554530,
		businessVoiceRoom: 33554530,
	}
	return m[business]
}

func isZWTemplateID(templateID int64) bool {
	return templateID == 33554530
}

func getBusiness(templateID int64, pluginType int32) int {
	switch templateID {
	case 268435460:
		return businessJY
	case 33554522:
		return businessPK
	case 268435466:
		return businessBaby
	case 33554530:
		return int(pluginType)
	}
	return 0
}

func getTerminalName(terminal int) string {
	switch terminal {
	case zwTerminal:
		return "追玩APP"
	case pcGameTerminal:
		return "PC导流专区"
	case yoTerminal:
		return "Yo 语音"
	case yoGameTerminal:
		return "Yo 开黑"
	case yayaTerminal:
		return "yaya"
	}
	return ""
}

// 判断业务id是否属于追玩业务
func isZhuiWanChannel(business int) bool {
	return business == businessBasic ||
		business == businessPW ||
		business == businessGame ||
		business == businessVoiceRoom ||
		business == businessGameTeam ||
		business == businessSkillCard
}

type tabInfo struct {
	Terminal int
	Category int
}

func getTerminalID(name string) int {
	switch strings.TrimSpace(name) {
	case "追玩APP":
		return zwTerminal
	case "PC导流专区":
		return pcGameTerminal
	}
	return 0
}

func getCategoryID(terminal int, name string) int {
	switch terminal {
	case zwTerminal:
		switch strings.TrimSpace(name) {
		case "娱乐":
			return categoryRecreation
		case "游戏":
			return categoryGame
		case "发现页":
			return categoryDiscovery
		case "分流推荐":
			return categoryFeed
		}
	case pcGameTerminal:
		switch strings.TrimSpace(name) {
		case "首页":
			return pcGameCategoryHome
		case "交友互动":
			return pcGameCategoryDating
		case "开黑陪玩":
			return pcGameCategoryTeam
		}
	}
	return 0
}

func getDefaultAvatar(terminal int) string {
	switch terminal {
	case zwTerminal:
		return "https://peiwan.bs2dl.yy.com/yo_jy_default.png"
	case yoTerminal, yoGameTerminal:
		return "https://lxcode.bs2cdn.yy.com/6be01a82-daad-4cda-b0cb-a4721edb982d.png"
	}
	return ""
}

func appTerminal(mark *fts.HTTPHeaderMark) int {
	terminal := zwTerminal
	switch {
	case mark.IsYoHostName():
		terminal = yoTerminal
	case mark.IsYoKHHostName():
		terminal = yoGameTerminal
	case mark.IsYaYaHostName():
		terminal = yayaTerminal
	}
	return terminal
}

// hostname转业务类型
func hostNameToBusiness(mark *fts.HTTPHeaderMark) (business int64) {
	business = constinfo.BusinessTypeZW

	if mark.IsYoHostName() {
		business = constinfo.BusinessTypeYOMI
	} else if mark.IsYoKHHostName() {
		business = constinfo.BusinessTypeYOKH
	} else if mark.IsYaYaHostName() {
		business = constinfo.BusinessTypeYAYA
	} else if mark.IsYoJYHostName() {
		business = constinfo.BusinessTypeZW
	}
	return
}

// 头部header转化为枚举值
func platformHeaderToPlatform(mark *fts.HTTPHeaderMark) (platform Platform) {
	if mark.IsPlatformIOS() {
		platform = platformIOS
	} else if mark.IsPlatformAndroid() {
		platform = platformAndroid
	} else if mark.IsPlatformPCWeb() {
		platform = platformPCWeb
	} else if mark.IsPlatformPC() {
		platform = platformPC
	}

	return
}

// 获取 cookie 中的uid，无需进行验证的那种，只是为了打日志
func getCookieUID(c *gin.Context) int64 {
	// yyuid,sysop_privilege_global_user_id,sysop_privilege_user_id
	for _, name := range []string{"yyuid", "sysop_privilege_user_id", "sysop_privilege_global_user_id"} {
		if value, err := c.Cookie(name); err == nil && len(value) > 0 {
			var uid = fts.ToInt64(value)
			if uid > 0 {
				return uid
			}
		}
	}
	return 0
}

func toJson(a any) string {
	if a == nil {
		return "<nil>"
	}
	b, err := json.Marshal(a)
	if err != nil {
		return err.Error()
	}
	return string(b)
}

func getRecommendTabConfig(id int64) (mgodao.RecommendTabConfig, bool) {
	value, ok := recommendTabConfig.Load(id)
	if !ok {
		return mgodao.RecommendTabConfig{}, false
	}
	config, ok := value.(mgodao.RecommendTabConfig)
	return config, ok
}
