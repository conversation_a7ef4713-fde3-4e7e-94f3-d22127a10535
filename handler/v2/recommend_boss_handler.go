package v2

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/globalsign/mgo"

	"git.yy.com/golang/gfy/v2/yy/yylog"
	"go.uber.org/zap"

	"github.com/gin-gonic/gin"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/contractdao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/webdbdao"
	"git.yy.com/server/jiaoyou/go_projects/api/common/constinfo"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
	"git.yy.com/server/jiaoyou/go_projects/api/common/fts/gin/middleware"
	"git.yy.com/server/jiaoyou/go_projects/api/common/util"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_ch_popular"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-thrift/contract"
	mgodao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/mgodao/v2"
	"github.com/tealeg/xlsx"
)

const (
	none = 0

	liveState   = 1 // 开播中
	unLiveState = 2 // 未开播

	liveVideo = 1 // 视频开播
	liveAudio = 2 // 音频开播
)

type recommendManageConfigQuery struct {
	Category   int `form:"category"`
	ConfigMode int `form:"config_mode"`
	Terminal   int `form:"terminal"`
}

type recommendListQuery struct {
	LiveState         int    `form:"livestate"`
	ZoneID            int64  `form:"zoneid"`
	ID                int64  `form:"id"`
	UID               int64  `form:"uid"`
	Sid               int64  `form:"sid"`
	Ssid              int64  `form:"ssid"`
	Mode              int    `form:"mode"`
	Platform          int    `form:"platform"`
	Business          int    `form:"business"`
	Terminal          int    `form:"terminal"`
	RecommendManageID string `form:"recommend_manage_id"`
}

type recommendListRespItem struct {
	mgodao.RecommendItem
	Nick      string `json:"nick"`
	LiveState int    `json:"live_state"`
	Cover     string `json:"cover"`
	Title     string `json:"title"`
	Label     string `json:"label"`
	Psu       int64  `json:"psu"`
	Asid      int64  `json:"asid,omitempty"`
	GameType  string `json:"game_type,omitempty"` // 开播的玩法
	LiveType  int    `json:"live_type"`
}

type recommendItemQuery struct {
	Mode              int    `form:"mode"`
	ZoneID            int64  `form:"zoneid"`
	Position          int64  `form:"position"`
	ConfigMode        int    `form:"config_mode"`
	Terminal          int    `form:"terminal"`
	RecommendManageID string `form:"recommend_manage_id"`
}

type recommendTabConfigQuery struct {
	Terminal int `form:"terminal"`
	Category int `form:"category"`
	Platform int `form:"platform"`
}

type opGetUserEnterChannelInfoQuery struct {
	UID int64 `form:"uid"`
}

// 判断推荐逻辑id是否已知
func isValidRecommendLogic(logic int) bool {
	for _, v := range []int{
		recommendByConfig,
		recommendByJYOnlineInfo,
		recommendByPKOnlineInfo,
		recommendByBabyOnlineInfo,
		recommendHitHotInfo,
		recommendVideoDatingGameType,
		recommendGameTeam,
		recommendChatRoom,
		recommendVideo,
		recommendAudio,
		recommendNewCompere,
		recommendHonorOfKings,
		recommendPUBGMobile,
		recommendTeamFightTactics,
		recommendMiniGame,
		recommendReception,
		recommendBySupplement,
		recommendAllOnlineInfo,
		recommendSkillCard,
		recommendByJYAndBabyOnlineInfo,
		recommendGameQueue,
		recommendUgcOnline,
		recommendUgcKaihei,
		recommendUgcChat,
		recommendUgcGame,
		recommendUgcWzry,
		recommendUgcPubg,
		recommendUgcJcczz,
	} {
		if v == logic {
			return true
		}
	}
	return false
}

// 推荐位置范围检验
func isValidRecommendPosition(positionList []int64) bool {
	if len(positionList) == 0 {
		return false
	}
	for _, position := range positionList {
		if position <= 0 || position >= 100 {
			return false
		}
	}
	return true
}

func parseInt64(s string) (int64, error) {
	if len(s) == 0 {
		return 0, nil
	}
	return strconv.ParseInt(s, 10, 64)
}

func parseInt(s string) (int, error) {
	if len(s) == 0 {
		return 0, nil
	}
	return strconv.Atoi(s)
}

// OpGetZhuiyaRecommendManageConfig 推荐管理配置
func OpGetZhuiyaRecommendManageConfig(c *gin.Context) {
	var ret = struct {
		fts.RespHeader
		List    []mgodao.RecommendManageConfig `json:"list"`
		TabList []mgodao.RecommendTabConfig    `json:"tabList"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	var err error
	var query recommendManageConfigQuery
	if err = c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	ret.List, err = mgodao.GetRecommendManageConfig(query.Terminal, query.Category, query.ConfigMode, 0)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	ret.TabList, err = mgodao.GetRecommendTabConfig(query.Terminal, 0, 0)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	yylog.Info("", fts.UID(uid), zap.Any("query", query), zap.Int("len(list)", len(ret.List)))
}

// 检查导航推荐配置字段是否填写有误
func validatorRecommendManageConfigOtherField(info *mgodao.RecommendManageConfig) (retCode int) {
	if !isValidPage(info.Terminal, info.Category) {
		return statusInvalidRecommendCategory
	}
	if info.IsAuditConfigMode() {
		if !isValidRecommendPosition(info.PositionList) {
			return statusInvalidRecommendPosition
		}
		if !info.IsValidRecommendStatus() {
			return statusInvalidRecommendStatus
		}
	} else if !info.IsValidLabelStatus() {
		return statusInvalidLabelStatus
	}
	return fts.StatusOK
}

// OpAddOrUpdateZhuiyaRecommendManageConfig 添加或更新推荐管理配置
func OpAddOrUpdateZhuiyaRecommendManageConfig(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	var info mgodao.RecommendManageConfig
	// 校验字段
	if err := c.ShouldBindJSON(&info); err != nil {
		status := errorWrapper(err)
		ret.SetStatus(status, statusText[status])
		return
	}
	if retCode := validatorRecommendManageConfigOtherField(&info); retCode != fts.StatusOK {
		ret.SetStatus(retCode, statusText[retCode])
		return
	}
	positionMap := make(map[int64]bool)
	for _, position := range info.PositionList {
		if positionMap[position] {
			ret.SetStatus(statusPositionDup, fmt.Sprintf(statusText[statusPositionDup], position))
			return
		}
		positionMap[position] = true
	}
	configs, err := mgodao.GetRecommendManageConfig(0, 0, 0, info.ZoneID)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	for _, config := range configs {
		if config.ID == info.ID {
			continue
		}
		for _, position := range config.PositionList {
			if positionMap[position] {
				ret.SetStatus(statusPositionDup, fmt.Sprintf(statusText[statusPositionDup], position))
				return
			}
		}
	}
	if info.IsAutoRecommendConfigMode() {
		info.PositionList = []int64{0}
	}
	if c.GetInt(Action) == BoosActionUpdate {
		if err := mgodao.UpdateRecommendManageConfig(&info); err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
	} else {
		isDup, _, err := mgodao.AddRecommendManageConfig(&info)
		if err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		if isDup {
			ret.SetStatus(fts.StatusRecordDup)
			return
		}
	}
	yylog.Info("update recommend manage config", fts.UID(uid), zap.Any("info", info))
}

// OpRemoveZhuiyaRecommendManageConfig 删除推荐管理配置
func OpRemoveZhuiyaRecommendManageConfig(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	info := struct {
		ID     string `json:"id"`
		ZoneID int64  `json:"zoneid"`
	}{}
	if err := c.ShouldBindJSON(&info); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	if err := mgodao.RemoveRecommendManageConfig(info.ID); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	if err := mgodao.RemoveRecommendItemByManageID(info.ID); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	if err := mgodao.RemoveFormulaByZoneID(info.ZoneID); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	yylog.Info("remove recommend manage config", fts.UID(uid), zap.Any("info", info))
}

func gameTypeChineseName(gameType int) string {
	var r string
	switch gameType {
	case constinfo.KOrdinary:
		r = "相亲"
	case constinfo.KVideoDating:
		r = "多人视频"
	case constinfo.KChannelFight:
		r = "乱斗"
	case constinfo.KTeamFight:
		r = "团战"
	case constinfo.KBirthday:
		r = "生日房"
	case constinfo.KVideoPk:
		r = "多人视频PK"
	case constinfo.KVideoFightMatch:
		r = "多人视频乱斗"
	default:
		r = strconv.FormatInt(int64(gameType), 10)
	}
	return r
}

func getZhuiyaRecommendList(id int64, oi onlineInfo, items []mgodao.RecommendItem) (
	[]recommendListRespItem,
	map[int64]int64,
	map[int64]string,
	map[Channel]int64,
	map[string]string,
	map[string]string,
	map[string]string) {
	var uids []int64
	var sids []int64
	var channels []Channel
	var gameChannels []Channel
	var ids []string
	var channelIDs []string
	seenUID := make(map[int64]bool)
	seenSid := make(map[int64]bool)
	seenChannel := make(map[Channel]bool)
	seenIDs := make(map[string]bool)
	seenChannelIDs := make(map[string]bool)

	f := func(uid, sid, ssid int64, business int) {
		if uid > 0 && !seenUID[uid] {
			seenUID[uid] = true
			uids = append(uids, uid)
		}
		if sid > 0 && !seenSid[sid] {
			seenSid[sid] = true
			sids = append(sids, sid)
		}
		c := Channel{Sid: sid, Ssid: ssid}
		if sid > 0 && ssid > 0 && !seenChannel[c] {
			seenChannel[c] = true
			channels = append(channels, c)
			if isZhuiWanChannel(business) {
				gameChannels = append(gameChannels, c)
			}
		}
		if uid > 0 {
			id := fmt.Sprintf("%d_%d", uid, business)
			if !seenIDs[id] {
				seenIDs[id] = true
				ids = append(ids, id)
			}
		}
		if sid > 0 && ssid > 0 {
			id := fmt.Sprintf("%d_%d_%d", sid, ssid, business)
			if !seenChannelIDs[id] {
				seenChannelIDs[id] = true
				channelIDs = append(channelIDs, id)
			}
		}
	}

	var records []recommendListRespItem
	var terminal int
	for i := range items {
		var r recommendListRespItem
		r.LiveState = unLiveState
		if items[i].IsRecommendCompere() {
			c := oi.getChannel(items[i].UID)
			if c.Sid > 0 && c.Ssid > 0 {
				items[i].Sid = c.Sid
				items[i].Ssid = c.Ssid
				r.LiveState = liveState
			}
		} else {
			c := Channel{Sid: items[i].Sid, Ssid: items[i].Ssid}
			items[i].UID = oi.getUID(c)
			if items[i].UID > 0 {
				r.LiveState = liveState
			}
		}
		f(items[i].UID, items[i].Sid, items[i].Ssid, items[i].Business)
		r.RecommendItem = items[i]
		records = append(records, r)
		if terminal == 0 {
			terminal = r.Terminal
		}
	}

	asidMap, err := webdbdao.BatchGetAsidBySid(sids)
	if err != nil {
		yylog.Warn("cannot batch get asid by sid", zap.Error(err))
	}

	// 昵称
	nickMap, err := webdbdao.BatchGetUserNickInfo(uids)
	if err != nil {
		yylog.Warn("cannot batch get user nick info", zap.Error(err))
	}

	popularityMap, err := getRoomPopularityValue(id, terminal, channels)
	if err != nil {
		yylog.Warn("cannot get room popularity value", zap.Error(err))
	}

	remarksMap, _, err := batchGetGameChannelRemarksAndSubTitle(gameChannels)
	if err != nil {
		yylog.Warn("cannot batch get game channel remarks and sub title", zap.Error(err))
	}

	coverMap, titleMap, labelMap, _, err := getFeatureInfo(ids, channelIDs, remarksMap)
	if err != nil {
		yylog.Warn("cannot get feature info", zap.Error(err))
	}
	return records, asidMap, nickMap, popularityMap, coverMap, titleMap, labelMap
}

// OpGetZhuiyaRecommendList 推荐列表
func OpGetZhuiyaRecommendList(c *gin.Context) {
	var ret = struct {
		fts.RespHeader
		List []recommendListRespItem `json:"recommendList"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	var query recommendListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	yylog.Info("get recommend list", fts.UID(uid), zap.Any("query", query))

	items, err := mgodao.GetRecommendItem(query.RecommendManageID, query.Mode, query.Business)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	var oi onlineInfo
	onlineChannelInfo, videoChannel, err := getOnlineChannelInfo()
	if err != nil {
		yylog.Warn("getOnlineChannelInfo", zap.Error(err), fts.UID(uid), zap.Any("query", query))
		ret.SetStatus(fts.StatusServerError)
		return
	}
	for business, info := range onlineChannelInfo {
		oi.setOnlineInfo(info, business)
	}
	records, asidMap, nickMap, popularityMap, coverMap,
		titleMap, labelMap := getZhuiyaRecommendList(query.ID, oi, items)

	for _, r := range records {
		uid := r.UID
		if nickMap != nil && uid != 0 {
			r.Nick = nickMap[uid]
		}
		sid := r.Sid
		if asidMap != nil && sid != 0 {
			r.Asid = asidMap[sid]
		}
		if r.LiveState == liveState {
			c := Channel{Sid: r.Sid, Ssid: r.Ssid}
			item := viewItem{
				UID:      r.UID,
				Sid:      r.Sid,
				Ssid:     r.Ssid,
				Business: r.Business,
				Mode:     r.Mode,
				GameType: oi.getGameType(c),
			}
			r.Cover = getCoverOrTitle(coverMap, &item)
			r.Title = getCoverOrTitle(titleMap, &item)
			r.Label = channelFirst(labelMap, &item)

			if r.Business == businessJY {
				r.GameType = gameTypeChineseName(oi.getGameType(c))
			}
			if popularityMap != nil {
				r.Psu = popularityMap[c]
			}
			r.LiveType = liveAudio
			if videoChannel != nil && videoChannel[c] {
				r.LiveType = liveVideo
			}
		}
		// 开播状态筛选
		if query.LiveState != none && r.LiveState != query.LiveState {
			continue
		}

		// uid筛选
		if query.UID != none && query.UID != r.UID {
			continue
		}

		// sid筛选
		if query.Sid != none && query.Sid != r.Sid {
			continue
		}

		// ssid筛选
		if query.Ssid != none && query.Ssid != r.Ssid {
			continue
		}

		// business筛选
		if query.Business != none && query.Business != r.Business {
			continue
		}
		ret.List = append(ret.List, r)
	}
	sort.Slice(ret.List, func(i, j int) bool {
		if ret.List[i].Weight != ret.List[j].Weight {
			return ret.List[i].Weight > ret.List[j].Weight
		}
		return ret.List[i].Psu > ret.List[j].Psu
	})
	yylog.Info("get recommend list", fts.UID(uid), zap.Any("query", query), zap.Int("len(list)", len(ret.List)))
}

func validatorRecommendItemOtherField(info *mgodao.RecommendItem) (retCode int) {
	if info.IsRecommendChannel() && (info.Sid == 0 || info.Ssid == 0) {
		return statusInvalidRecommendChannel
	} else if info.IsRecommendCompere() && info.UID == 0 {
		return statusInvalidRecommendUID
	}
	if !isValidPlatform(info.Terminal, info.Platform) {
		return statusRecommendInvalidPlatform
	}
	if !isValidBusiness(info.Business) {
		return statusInvalidBusiness
	}
	if !info.IsValidRecommendStatus() {
		return statusInvalidRecommendStatus
	}
	return fts.StatusOK
}

func getRecommendItemID(recommendChannelMode bool, sid, ssid, uid int64, rmcID string, platform int) string {
	if recommendChannelMode {
		return fmt.Sprintf("%d:%d:%s:%d", sid, ssid, rmcID, platform)
	}
	return fmt.Sprintf("%d:%s:%d", uid, rmcID, platform)
}

// OpAddOrUpdateZhuiyaRecommendItem 添加或更新推荐信息
func OpAddOrUpdateZhuiyaRecommendItem(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	var info mgodao.RecommendItem
	// 校验字段
	if err := c.ShouldBindJSON(&info); err != nil {
		status := errorWrapper(err)
		ret.SetStatus(status, statusText[status])
		return
	}
	if retCode := validatorRecommendItemOtherField(&info); retCode != fts.StatusOK {
		ret.SetStatus(retCode, statusText[retCode])
		return
	}

	if c.GetInt(Action) == BoosActionUpdate {
		if err := mgodao.UpdateRecommendItem(&info); err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
	} else {
		info.ID = getRecommendItemID(info.IsRecommendChannel(), info.Sid, info.Ssid, info.UID, info.RecommendManageID, info.Platform)
		isDup, err := mgodao.AddRecommendItem(&info)
		if err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		if isDup {
			ret.SetStatus(fts.StatusRecordDup)
			return
		}
	}
	yylog.Info("update recommend item", fts.UID(uid), zap.Any("info", info))
}

// OpRemoveZhuiyaRecommendItem 删除推荐信息
func OpRemoveZhuiyaRecommendItem(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	info := struct {
		Ids string `json:"ids"`
	}{}
	if err := c.ShouldBindJSON(&info); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	ids := strings.Split(info.Ids, ",")
	if len(ids) > 0 {
		if err := mgodao.RemoveRecommendItem(ids); err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		yylog.Info("remove recommend item", fts.UID(uid), zap.Any("info", info))
	}
}

// OpImportZhuiyaRecommendItem 批量导入推荐信息
func OpImportZhuiyaRecommendItem(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		Count int64 `json:"count"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	err := c.Request.ParseMultipartForm(32 << 20)
	if err != nil {
		ret.SetStatus(fts.StatusServerError)
		return
	}

	file, head, err := c.Request.FormFile("file")
	if err != nil {
		ret.SetStatus(statusUploadFileError)
		return
	}
	defer file.Close()

	if head == nil {
		return
	}
	yylog.Info("import recommend item", zap.String("filename", head.Filename), zap.Int64("size", head.Size), zap.Any("header", head.Header))

	content, err := ioutil.ReadAll(file)
	if len(content) == 0 {
		return
	}

	excelFile, err := xlsx.OpenBinary(content)
	if err != nil {
		ret.SetStatus(statusUploadFileError, statusText[statusUploadFileError])
		return
	}

	var query recommendItemQuery
	if err = c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	yylog.Info("import recommend item", zap.Any("query", query))

	var items []mgodao.RecommendItem
	for _, sheetData := range excelFile.Sheet {
		for index, row := range sheetData.Rows {
			if index == 0 {
				continue
			}
			var sid int64
			var ssid int64
			var uid int64
			var weight int64
			var business int
			var platform int
			var startTime int64
			var endTime int64
			var attributesStatus int
			var status int
			if mgodao.IsAutoRecommendConfigMode(query.ConfigMode) {
				if mgodao.IsRecommendChannel(query.Mode) {
					if len(row.Cells) != 5 {
						ret.SetStatus(statusExcelErrorFormatMsg,
							fmt.Sprintf(statusText[statusExcelErrorFormatMsg],
								index+1, statusText[statusExcelColumnsError]))
						return
					}
					sid, _ = strconv.ParseInt(row.Cells[0].Value, 10, 64)
					ssid, _ = strconv.ParseInt(row.Cells[1].Value, 10, 64)
					business, _ = strconv.Atoi(row.Cells[2].Value)
					platform, _ = strconv.Atoi(row.Cells[3].Value)
					status, _ = strconv.Atoi(row.Cells[4].Value)
				} else {
					if len(row.Cells) != 4 {
						ret.SetStatus(statusExcelErrorFormatMsg,
							fmt.Sprintf(statusText[statusExcelErrorFormatMsg],
								index+1, statusText[statusExcelColumnsError]))
						return
					}
					uid, _ = strconv.ParseInt(row.Cells[0].Value, 10, 64)
					business, _ = strconv.Atoi(row.Cells[1].Value)
					platform, _ = strconv.Atoi(row.Cells[2].Value)
					status, _ = strconv.Atoi(row.Cells[3].Value)
				}
			} else {
				if mgodao.IsRecommendChannel(query.Mode) {
					if len(row.Cells) != 9 {
						ret.SetStatus(statusExcelErrorFormatMsg,
							fmt.Sprintf(statusText[statusExcelErrorFormatMsg],
								index+1, statusText[statusExcelColumnsError]))
						return
					}
					sid, _ = strconv.ParseInt(row.Cells[0].Value, 10, 64)
					ssid, _ = strconv.ParseInt(row.Cells[1].Value, 10, 64)
					business, _ = strconv.Atoi(row.Cells[2].Value)
					platform, _ = strconv.Atoi(row.Cells[3].Value)
					weight, _ = strconv.ParseInt(row.Cells[4].Value, 10, 64)
					startTime, _ = strconv.ParseInt(row.Cells[5].Value, 10, 64)
					endTime, _ = strconv.ParseInt(row.Cells[6].Value, 10, 64)
					attributesStatus, _ = strconv.Atoi(row.Cells[7].Value)
					status, _ = strconv.Atoi(row.Cells[8].Value)
				} else {
					if len(row.Cells) != 8 {
						ret.SetStatus(statusExcelErrorFormatMsg,
							fmt.Sprintf(statusText[statusExcelErrorFormatMsg],
								index+1, statusText[statusExcelColumnsError]))
						return
					}
					uid, _ = strconv.ParseInt(row.Cells[0].Value, 10, 64)
					business, _ = strconv.Atoi(row.Cells[1].Value)
					platform, _ = strconv.Atoi(row.Cells[2].Value)
					weight, _ = strconv.ParseInt(row.Cells[3].Value, 10, 64)
					startTime, _ = strconv.ParseInt(row.Cells[4].Value, 10, 64)
					endTime, _ = strconv.ParseInt(row.Cells[5].Value, 10, 64)
					attributesStatus, _ = strconv.Atoi(row.Cells[6].Value)
					status, _ = strconv.Atoi(row.Cells[7].Value)
				}
			}
			item := mgodao.RecommendItem{
				UID:                uid,
				Sid:                sid,
				Ssid:               ssid,
				Platform:           platform,
				Weight:             weight,
				ZoneID:             query.ZoneID,
				Business:           business,
				Mode:               query.Mode,
				RecommendStartTime: startTime,
				RecommendEndTime:   endTime,
				AttributesStatus:   attributesStatus,
				RecommendStatus:    status,
				Terminal:           query.Terminal,
				RecommendManageID:  query.RecommendManageID,
			}
			item.ID = getRecommendItemID(item.IsRecommendChannel(), sid, ssid, uid, query.RecommendManageID, platform)
			// 校验字段
			if retCode := validatorRecommendItemOtherField(&item); retCode != fts.StatusOK {
				ret.SetStatus(statusExcelErrorFormatMsg,
					fmt.Sprintf(statusText[statusExcelErrorFormatMsg],
						index+1, statusText[retCode]))
				return
			}
			items = append(items, item)
		}
		break
	}

	if err := mgodao.BatchUpdateRecommendItem(items); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	ret.Count = int64(len(items))
	yylog.Info("import recommend item", zap.Any("query", query), zap.Any("ret", ret))
}

// OpExportZhuiyaRecommendItem 导出excel
func OpExportZhuiyaRecommendItem(c *gin.Context) {
	uid := c.GetInt64(middleware.StaffUID)

	var query recommendItemQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, http.StatusText(http.StatusBadRequest))
		return
	}
	yylog.Info("export recommend item", fts.UID(uid), zap.Any("query", query))

	items, err := mgodao.GetRecommendItem(query.RecommendManageID, query.Mode, 0)
	if err != nil {
		c.JSON(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError))
		return
	}

	// 按权重排序
	if len(items) > 0 {
		sort.Slice(items, func(i, j int) bool {
			if items[i].Weight > items[j].Weight {
				return true
			}
			return false
		})
	}

	f := xlsx.NewFile()
	sheet, err := f.AddSheet("Sheet1")
	if err != nil {
		return
	}

	var title []string
	if mgodao.IsAutoRecommendConfigMode(query.ConfigMode) {
		if mgodao.IsRecommendChannel(query.Mode) {
			title = []string{"频道", "子频道", "业务", "平台", "推荐状态"}
		} else {
			title = []string{"UID", "业务", "平台", "推荐状态"}
		}
	} else {
		if mgodao.IsRecommendChannel(query.Mode) {
			title = []string{"频道", "子频道", "业务", "平台", "权重", "推荐开始时间(时间戳)", "推荐结束时间(时间戳)", "属性状态", "推荐状态"}
		} else {
			title = []string{"UID", "业务", "平台", "权重", "推荐开始时间(时间戳)", "推荐结束时间(时间戳)", "属性状态", "推荐状态"}
		}
	}
	row := sheet.AddRow()
	_ = row.WriteSlice(&title, len(title))

	for _, item := range items {
		// 校验字段
		item.Terminal = query.Terminal
		retCode := validatorRecommendItemOtherField(&item)
		if retCode != fts.StatusOK {
			yylog.Info("failed validator recommend item other field", zap.Any("item", item), zap.Int("retCode", retCode))
			continue
		}

		var s []string
		if mgodao.IsAutoRecommendConfigMode(query.ConfigMode) {
			if mgodao.IsRecommendChannel(query.Mode) {
				s = []string{
					strconv.FormatInt(item.Sid, 10),
					strconv.FormatInt(item.Ssid, 10),
					strconv.FormatInt(int64(item.Business), 10),
					strconv.FormatInt(int64(item.Platform), 10),
					strconv.FormatInt(int64(item.RecommendStatus), 10),
				}
			} else {
				s = []string{
					strconv.FormatInt(item.UID, 10),
					strconv.FormatInt(int64(item.Business), 10),
					strconv.FormatInt(int64(item.Platform), 10),
					strconv.FormatInt(int64(item.RecommendStatus), 10),
				}
			}
		} else {
			if mgodao.IsRecommendChannel(query.Mode) {
				s = []string{
					strconv.FormatInt(item.Sid, 10),
					strconv.FormatInt(item.Ssid, 10),
					strconv.FormatInt(int64(item.Business), 10),
					strconv.FormatInt(int64(item.Platform), 10),
					strconv.FormatInt(item.Weight, 10),
					strconv.FormatInt(item.RecommendStartTime, 10),
					strconv.FormatInt(item.RecommendEndTime, 10),
					strconv.FormatInt(int64(item.AttributesStatus), 10),
					strconv.FormatInt(int64(item.RecommendStatus), 10),
				}
			} else {
				s = []string{
					strconv.FormatInt(item.UID, 10),
					strconv.FormatInt(int64(item.Business), 10),
					strconv.FormatInt(int64(item.Platform), 10),
					strconv.FormatInt(item.Weight, 10),
					strconv.FormatInt(item.RecommendStartTime, 10),
					strconv.FormatInt(item.RecommendEndTime, 10),
					strconv.FormatInt(int64(item.AttributesStatus), 10),
					strconv.FormatInt(int64(item.RecommendStatus), 10),
				}
			}
		}
		row := sheet.AddRow()
		_ = row.WriteSlice(&s, len(s))
	}

	var b bytes.Buffer
	w := bufio.NewWriter(&b)

	err = f.Write(w)
	if err != nil {
		return
	}
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	_, err = c.Writer.Write(b.Bytes())
	if err != nil {
		return
	}
	yylog.Info("export recommend item", fts.UID(uid), zap.Any("query", query), zap.Int("len(items)", len(items)))
}

// OpUpdateZhuiyaRecommendItemStatus 更新推荐状态
func OpUpdateZhuiyaRecommendItemStatus(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)
	uid := c.GetInt64(middleware.StaffUID)
	info := struct {
		ID              string `json:"id" binding:"required"`
		RecommendStatus int    `json:"recommend_status" binding:"gte=1,lte=2"` // 推荐状态
	}{}
	// 校验字段
	if err := c.ShouldBindJSON(&info); err != nil {
		status := errorWrapper(err)
		ret.SetStatus(status, statusText[status])
		return
	}
	if err := mgodao.UpdateRecommendItemStatus(info.ID, info.RecommendStatus); err != nil {
		ret.SetStatus(fts.StatusServerError)
		return
	}
	yylog.Info("update recommend item status", fts.UID(uid), zap.Any("info", info))
}

// ============= 导航管理 ====================

// OpGetZhuiyaRecommendTabConfig 查询导航配置
func OpGetZhuiyaRecommendTabConfig(c *gin.Context) {
	type Item struct {
		mgodao.RecommendTabConfig
		GameID string `json:"gameId"`
	}
	var ret = struct {
		fts.RespHeader
		List []Item `json:"list"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	var query recommendTabConfigQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	records, err := mgodao.GetRecommendTabConfig(query.Terminal, query.Category, query.Platform)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	for _, r := range records {
		var gameID string
		if len(r.GameIDList) > 0 {
			var b strings.Builder
			_, _ = b.WriteString(strconv.Itoa(r.GameIDList[0]))
			for _, n := range r.GameIDList[1:] {
				_, _ = b.WriteString("|")
				_, _ = b.WriteString(strconv.Itoa(n))
			}
			gameID = b.String()
		}
		ret.List = append(ret.List, Item{
			RecommendTabConfig: r,
			GameID:             gameID,
		})
	}
	sort.Slice(ret.List, func(i, j int) bool {
		return ret.List[i].Weight > ret.List[j].Weight
	})
	yylog.Info("get recommend tab config", fts.UID(uid), zap.Any("query", query), zap.Int("len(list)", len(ret.List)))
}

func isValidVersion(version string) bool {
	if len(version) == 0 {
		return true
	}
	s := strings.Split(version, ".")
	if len(s) != 3 {
		return false
	}
	for _, ss := range s {
		if _, err := strconv.Atoi(ss); err != nil {
			return false
		}
	}
	return true
}

func validatorRecommendTabConfigOtherField(info *mgodao.RecommendTabConfig) (retCode int) {
	if !isValidPage(info.Terminal, info.Category) {
		return statusInvalidRecommendCategory
	}
	if !isValidPlatform(info.Terminal, info.Platform) {
		return statusRecommendInvalidPlatform
	}
	if !isValidVersion(info.StartVersion) || !isValidVersion(info.EndVersion) || !info.IsValidVersionSelect() || !info.StartVersionLessThanEndVersion() {
		return statusInvalidVersion
	}
	if !isValidRecommendGameType(info.GameType) {
		return statusInvalidGameType
	}
	if !isValidRecommendRoomType(info.RoomType) {
		return statusInvalidRoomType
	}
	if !isValidRecommendLogic(info.Logic) {
		return statusInvalidRecommendLogic
	}
	if isValidCategory(info.Category, categoryInsertModule) && (info.InsertedTab.Position <= 0 ||
		info.InsertedTab.Position > 100 || info.InsertedTab.Position%2 != 0) {
		return statusInvalidModuleInsertedPosition
	}
	return fts.StatusOK
}

// OpAddOrUpdateZhuiyaRecommendTabConfig 添加或更新导航配置
func OpAddOrUpdateZhuiyaRecommendTabConfig(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	info := struct {
		mgodao.RecommendTabConfig
		GameID string `json:"gameId"`
	}{}
	// 校验字段
	if err := c.ShouldBindJSON(&info); err != nil {
		status := errorWrapper(err)
		ret.SetStatus(status, statusText[status])
		return
	}
	// 校验字段
	if retCode := validatorRecommendTabConfigOtherField(&info.RecommendTabConfig); retCode != fts.StatusOK {
		ret.SetStatus(retCode, statusText[retCode])
		return
	}
	if info.Category == categoryCloudGame {
		for _, s := range strings.Split(info.GameID, "|") {
			ss := strings.Trim(s, " ")
			if len(ss) == 0 {
				ret.SetStatus(statusInvalidGameID, statusText[statusInvalidGameID])
				return
			}
			id, err := strconv.Atoi(ss)
			if err != nil {
				ret.SetStatus(statusInvalidGameID, statusText[statusInvalidGameID])
				return
			}
			info.GameIDList = append(info.GameIDList, id)
		}
	}
	if c.GetInt(Action) == BoosActionUpdate {
		if err := mgodao.UpdateRecommendTabConfig(&info.RecommendTabConfig); err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
	} else {
		used, err := mgodao.FindRecommendTabConfigUsedID(info.Terminal)
		if err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		if info.Category == categoryInsertModule {
			info.ID = int64(moduleStartID*info.InsertedTab.Position) + info.InsertedTab.ID
		} else {
			terminalStartID := int64(info.Terminal * terminalIDStep)
			terminalEndID := terminalStartID + terminalIDStep
			id := terminalStartID
			for ; id < terminalEndID; id += tabIDStep {
				if _, ok := used[id]; !ok {
					break
				}
			}
			if id >= terminalEndID {
				ret.SetStatus(statusTabIDUpperLimit, statusText[statusTabIDUpperLimit])
				return
			}
			info.ID = id
		}
		isDup, err := mgodao.AddRecommendTabConfig(&info.RecommendTabConfig)
		if err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		if isDup {
			ret.SetStatus(fts.StatusRecordDup)
			return
		}
	}
	yylog.Info("update recommend tab config", fts.UID(uid), zap.Any("info", info))
}

// OpRemoveZhuiyaRecommendTabConfig 删除导航配置
func OpRemoveZhuiyaRecommendTabConfig(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	info := struct {
		ID int64 `json:"id"`
	}{}
	if err := c.ShouldBindJSON(&info); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	if err := mgodao.RemoveRecommendTabConfig(info.ID); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	configs, err := mgodao.GetRecommendManageConfig(0, 0, 0, info.ID)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	for _, config := range configs {
		if err := mgodao.RemoveRecommendManageConfig(config.ID); err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		if err := mgodao.RemoveRecommendItemByManageID(config.ID); err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		yylog.Info("remove recommend tab config", fts.UID(uid), zap.Any("config", config))
	}
	yylog.Info("remove recommend tab config", fts.UID(uid), zap.Any("info", info))
}

// OpCopyZhuiyaRecommendTabConfig 复制tab
func OpCopyZhuiyaRecommendTabConfig(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	info := struct {
		SrcID  int64 `json:"srcId" binding:"gt=0"`
		DestID int64 `json:"destId" binding:"gt=0"`
	}{}
	if err := c.ShouldBindJSON(&info); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	srcConfig, err := mgodao.GetRecommendTabConfigByID(info.SrcID)
	if err == mgo.ErrNotFound {
		ret.SetStatus(statusSrcIDNotFound, statusText[statusSrcIDNotFound])
		return
	}
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	srcRecommendManageConfigs, err := mgodao.GetRecommendManageConfig(0, 0, 0, info.SrcID)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	destRecommendManageConfigs, err := mgodao.GetRecommendManageConfig(0, 0, 0, info.DestID)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	if len(destRecommendManageConfigs) != 0 {
		ret.SetStatus(statusDestIDNotFound, statusText[statusDestIDNotFound])
		return
	}

	srcConfig.ID = info.DestID
	isDup, err := mgodao.AddRecommendTabConfig(&srcConfig)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	if isDup {
		ret.SetStatus(fts.StatusRecordDup)
		return
	}

	for _, srmc := range srcRecommendManageConfigs {
		srcSrmcID := srmc.ID
		srmc.ZoneID = info.DestID
		_, id, err := mgodao.AddRecommendManageConfig(&srmc)
		if err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		srcItems, err := mgodao.GetRecommendItem(srcSrmcID, 0, 0)
		if err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		yylog.Info("after get recommend item", zap.String("id", srcSrmcID), zap.Int("len(srcItems)", len(srcItems)))
		for _, item := range srcItems {
			item.ID = getRecommendItemID(item.IsRecommendChannel(), item.Sid, item.Ssid, item.UID, id, item.Platform)
			item.ZoneID = info.DestID
			item.RecommendManageID = id
			isDup, err := mgodao.AddRecommendItem(&item)
			if err != nil {
				ret.SetStatus(fts.StatusAccessDBError)
				return
			}
			if isDup {
				ret.SetStatus(fts.StatusRecordDup)
				return
			}
		}
	}
}

// =================================

// OpGetFormulaParameter 获取公式参数
func OpGetFormulaParameter(c *gin.Context) {
	rw, req := c.Writer, c.Request
	var ret = struct {
		fts.RespHeader
		Payload mgodao.FormulaParameter `json:"payload"`
	}{}
	defer util.HttpRespJson(rw, req, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	zoneid, err := parseInt(req.URL.Query().Get("zoneid"))
	if err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	ret.Payload, err = mgodao.GetFormulaParameter(zoneid)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	yylog.Info("", fts.UID(uid), zap.Any("ret", ret))
}

// OpSetFormulaParameter 设置公式参数
func OpSetFormulaParameter(c *gin.Context) {
	rw, req := c.Writer, c.Request
	var ret fts.RespHeader
	defer util.HttpRespJson(rw, req, &ret)

	var info mgodao.FormulaParameter
	if err := json.NewDecoder(req.Body).Decode(&info); err != nil {
		ret.SetStatus(fts.StatusJSONParseError)
		return
	}

	info.ID = strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	if err := mgodao.SetFormulaParameter(info); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	yylog.Info("", zap.Any("info", info))
}

// GetOpPopularityValueConf 查询运营人气配置
func GetOpPopularityValueConf(c *gin.Context) {
	rw, req := c.Writer, c.Request
	var ret = struct {
		fts.RespHeader
		Payload []mgodao.OpPopularityValueConf `json:"payload"`
	}{}
	defer util.HttpRespJson(rw, req, &ret)

	sid, err := parseInt64(req.URL.Query().Get("sid"))
	if err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	ret.Payload, err = mgodao.GetOpPopularityValueConf(sid)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	yylog.Info("", zap.Any("ret", ret))
}

// SetOpPopularityValueConf 设置运营人气配置
func SetOpPopularityValueConf(c *gin.Context) {
	rw, req := c.Writer, c.Request
	var ret fts.RespHeader
	defer util.HttpRespJson(rw, req, &ret)

	var info mgodao.OpPopularityValueConf
	if err := json.NewDecoder(req.Body).Decode(&info); err != nil {
		ret.SetStatus(fts.StatusJSONParseError)
		return
	}

	if err := mgodao.SetOpPopularityValueConf(info); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	yylog.Info("", zap.Any("info", info))
}

// OpGetZhuiyaRecommendingList 实时推荐数据
func OpGetZhuiyaRecommendingList(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		List    []*viewItem                 `json:"list"`
		TabList []mgodao.RecommendTabConfig `json:"tabList"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	var query recommendListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	yylog.Info("get recommending list", zap.Any("query", query))

	var err error
	ret.TabList, err = mgodao.GetRecommendTabConfig(0, 0, 0)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	var tabID []int64
	for _, tab := range ret.TabList {
		// terminal筛选
		if query.Terminal != none && query.Terminal != tab.Terminal {
			continue
		}
		if query.ID != none && query.ID != tab.ID {
			continue
		}
		tabID = append(tabID, tab.ID)
	}

	var platform []int
	if query.Platform == 0 {
		platform = append(platform, platformAndroid, platformIOS, platformPC)
	} else {
		platform = append(platform, query.Platform)
	}

	var vi []*viewItem
	for _, id := range tabID {
		for _, p := range platform {
			items, _, err := getRecommendInfo(c.Request.Context(), id, p, query.Terminal, fts.Version{}, -1)
			if err != nil {
				ret.SetStatus(fts.StatusAccessRedisError)
				return
			}
			for _, item := range items {
				item.Platform = p
				vi = append(vi, item)
			}
		}
	}
	for _, item := range vi {
		// uid筛选
		if query.UID != none && query.UID != item.UID {
			continue
		}

		// sid筛选
		if query.Sid != none && query.Sid != item.Sid {
			continue
		}

		// ssid筛选
		if query.Ssid != none && query.Ssid != item.Ssid {
			continue
		}

		// business筛选
		if query.Business != none && query.Business != item.Business {
			continue
		}
		ret.List = append(ret.List, item)
	}
	yylog.Info("get recommending list", zap.Int("len(list)", len(ret.List)))
}

// OpGetZhuiyaRecommendBlackList 获取推荐黑名单
func OpGetZhuiyaRecommendBlackList(c *gin.Context) {
	var ret = struct {
		fts.RespHeader
		List []mgodao.RecommendBlackList `json:"list"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	query := struct {
		Type int `form:"type"`
	}{}
	err := c.ShouldBindQuery(&query)
	if err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	bl := mgodao.RecommendBlackList{Type: query.Type}
	if !bl.IsRecommendBlackListType() {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	ret.List, err = mgodao.GetRecommendBlackList(query.Type)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	if bl.IsUIDBlackListType() {
		ret.List = uidBlackListEnhance(ret.List)
	}
	yylog.Info("get recommend black list", fts.UID(uid), zap.Int("len(list)", len(ret.List)))
}

func uidBlackListEnhance(data []mgodao.RecommendBlackList) []mgodao.RecommendBlackList {
	var uidList []int64
	for _, info := range data {
		uidList = append(uidList, info.UID)
	}

	var err error
	var yyMap map[int64]int64
	var wg util.WaitGroupWrapper
	wg.Wrap(func() {
		yyMap, err = webdbdao.BatchGetImidByUid(uidList)
		if err != nil {
			yylog.Warn("BatchGetImidByUid", zap.Error(err))
		}
	})

	var contractMap map[int64]*contract.TContract
	wg.Wrap(func() {
		contractMap, err = contractdao.BatchQueryContractByAnchors(context.Background(), uidList)
		if err != nil {
			yylog.Warn("BatchQueryContractByAnchors", zap.Error(err))
		}
	})
	wg.Wait()

	var sidList []int64
	for _, c := range contractMap {
		sidList = append(sidList, c.GetSid())
	}
	asidMap, err1 := webdbdao.BatchGetAsidBySid(sidList)
	if err1 != nil {
		yylog.Warn("BatchGetAsidBySid", zap.Error(err1))
	}

	for i, info := range data {
		if yy, ok := yyMap[info.UID]; ok {
			data[i].YY = yy
		}

		var sid int64
		if c, ok := contractMap[info.UID]; ok {
			sid = c.GetSid()
		}

		if asid, ok := asidMap[sid]; ok {
			data[i].ASID = asid
		}
	}

	sort.SliceStable(data, func(i, j int) bool {
		if data[i].CreateAt > data[j].CreateAt {
			return true
		}

		return data[i].CreateAt == data[j].CreateAt && data[i].UID > data[j].UID
	})

	return data
}

func getBlackListID(info *mgodao.RecommendBlackList) string {
	switch {
	case info.IsSidBlackListType():
		return fmt.Sprintf("%d:%d", info.Sid, info.Type)
	case info.IsChannelBlackList():
		return fmt.Sprintf("%d:%d:%d", info.Sid, info.Ssid, info.Type)
	case info.IsUIDBlackListType():
		return getUIDBlackID(info.UID, info.Type)
	}
	return ""
}

func getUIDBlackID(uid int64, blackType int) string {
	return fmt.Sprintf("%d:%d", uid, blackType)
}

// OpAddZhuiyaRecommendBlackList 添加黑名单
func OpAddZhuiyaRecommendBlackList(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)
	userName := c.GetString(middleware.StaffPassport)

	var info mgodao.RecommendBlackList
	if err := c.ShouldBindJSON(&info); err != nil {
		ret.SetStatus(fts.StatusJSONParseError)
		return
	}
	if !info.IsRecommendBlackListType() {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	if info.IsUIDBlackListType() {
		info.CreateAt = time.Now().Unix()
		info.Creator = userName
		if len(info.Remark) == 0 {
			info.Remark = "人工"
		}
	}

	info.ID = getBlackListID(&info)
	isDup, err := mgodao.AddRecommendBlackList(&info)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	if isDup {
		ret.SetStatus(fts.StatusRecordDup)
		return
	}
	yylog.Info("add recommend black list", fts.UID(uid), zap.Any("info", info))
}

// OpRemoveZhuiyaRecommendBlackList 删除黑名单
func OpRemoveZhuiyaRecommendBlackList(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)

	info := struct {
		ID string `json:"id"`
	}{}
	if err := c.ShouldBindJSON(&info); err != nil {
		ret.SetStatus(fts.StatusJSONParseError)
		return
	}
	ids := strings.Split(info.ID, ",")
	if len(ids) > 0 {
		if err := mgodao.RemoveRecommendBlackList(ids); err != nil {
			ret.SetStatus(fts.StatusAccessDBError)
			return
		}
		yylog.Info("add recommend black list", fts.UID(uid), zap.Any("info", info))
	}
}

// OpImportZhuiyaRecommendBlackList 导入黑名单
func OpImportZhuiyaRecommendBlackList(c *gin.Context) {
	var ret fts.RespHeader
	defer c.JSON(http.StatusOK, &ret)

	uid := c.GetInt64(middleware.StaffUID)
	userName := c.GetString(middleware.StaffPassport)

	err := c.Request.ParseMultipartForm(32 << 20)
	if err != nil {
		ret.SetStatus(fts.StatusServerError)
		return
	}

	file, head, err := c.Request.FormFile("file")
	if err != nil {

		ret.SetStatus(statusUploadFileError)
		return
	}
	defer file.Close()

	if head == nil {
		return
	}
	yylog.Info("import recommend black list", fts.UID(uid), zap.String("filename", head.Filename),
		zap.Int64("size", head.Size), zap.Any("header", head.Header))

	content, err := ioutil.ReadAll(file)
	if len(content) == 0 {
		return
	}

	excelFile, err := xlsx.OpenBinary(content)
	if err != nil {
		ret.SetStatus(statusUploadFileError, statusText[statusUploadFileError])
		return
	}

	query := struct {
		Type int `form:"type"`
	}{}
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	bl := mgodao.RecommendBlackList{Type: query.Type}
	if !bl.IsRecommendBlackListType() {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}
	yylog.Info("import recommend black list", fts.UID(uid), zap.String("filename", head.Filename),
		zap.Int64("size", head.Size), zap.Any("header", head.Header), zap.Int("t", query.Type))

	nowInSec := time.Now().Unix()
	var items []mgodao.RecommendBlackList
	for _, sheetData := range excelFile.Sheet {
		for index, row := range sheetData.Rows {
			if index == 0 {
				continue
			}
			var sid int64
			var ssid int64
			var uid int64
			var err error
			var remark string
			var createAt int64
			var creator string
			parseInt64 := func(s string) (n int64) {
				if err != nil {
					return
				}
				n, err = strconv.ParseInt(s, 10, 64)
				return
			}

			switch {
			case bl.IsSidBlackListType():
				if len(row.Cells) != 1 {
					ret.SetStatus(statusExcelErrorFormatMsg, fmt.Sprintf(statusText[statusExcelErrorFormatMsg], index+1, statusText[statusExcelColumnsError]))
					return
				}
				sid = parseInt64(row.Cells[0].Value)
			case bl.IsChannelBlackList():
				if len(row.Cells) != 2 {
					ret.SetStatus(statusExcelErrorFormatMsg, fmt.Sprintf(statusText[statusExcelErrorFormatMsg], index+1, statusText[statusExcelColumnsError]))
					return
				}
				sid = parseInt64(row.Cells[0].Value)
				ssid = parseInt64(row.Cells[1].Value)
			case bl.IsUIDBlackListType():
				if len(row.Cells) < 1 {
					ret.SetStatus(statusExcelErrorFormatMsg, fmt.Sprintf(statusText[statusExcelErrorFormatMsg], index+1, statusText[statusExcelColumnsError]))
					return
				}
				uid = parseInt64(row.Cells[0].Value)
				if len(row.Cells) > 1 {
					remark = row.Cells[1].Value
				}
				createAt = nowInSec
				creator = userName
				if len(remark) == 0 {
					remark = "人工"
				}
			}
			if err != nil {
				ret.SetStatus(statusExcelErrorFormatMsg, fmt.Sprintf(statusText[statusExcelErrorFormatMsg], index+1, statusText[statusAddRecommendInputError]))
				return
			}
			item := mgodao.RecommendBlackList{
				UID:      uid,
				Sid:      sid,
				Ssid:     ssid,
				Type:     query.Type,
				Remark:   remark,
				Creator:  creator,
				CreateAt: createAt,
			}
			item.ID = getBlackListID(&item)
			items = append(items, item)
		}
		break
	}

	if err := mgodao.BatchUpdateRecommendBlackList(items); err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	yylog.Info("import recommend black list", fts.UID(uid), zap.Int("t", query.Type))
}

// 根据id获取导航栏配置的信息
func getTerminalInfo(id int64) (terminal, platform int, name, page string) {
	value, ok := recommendTabConfig.Load(id)
	if !ok {
		return 0, 0, "", ""
	}
	config, ok := value.(mgodao.RecommendTabConfig)
	if ok {
		platform = config.Platform
		terminal = config.Terminal
		name = config.Name
		page = getPageName(terminal, config.Category)
	}
	yylog.Debug("", zap.Any("config", config))
	return terminal, platform, name, page
}

// 获取category对应的推荐tab名称
func getPageName(terminal, category int) (page string) {
	if terminal == zwTerminal || terminal == yoTerminal || terminal == yoGameTerminal {
		switch category {
		case categoryRecreation:
			page = "娱乐"
		case categoryGame:
			page = "游戏"
		case categoryDiscovery:
			page = "发现页"
		case categoryFeed:
			page = "分流推荐"
		case categoryCloudGame:
			page = "云游戏"
		case categoryGameRegion:
			page = "游戏专区"
		case categoryChatRoom:
			page = "聊天室"
		case categoryDynamicLanding:
			page = "动态落地页"
		case categorySidebar:
			page = "房间内侧边栏"
		case categoryH5VoiceRoom:
			page = "H5语音房"
		case categoryPopularRecommend:
			page = "人气推荐"
		case categoryYoGameCenter:
			page = "游戏专区"
		}
	} else if terminal == pcGameTerminal {
		switch category {
		case pcGameCategoryHome:
			page = "首页"
		case pcGameCategoryDating:
			page = "交友互动"
		case pcGameCategoryTeam:
			page = "开黑陪玩"
		}
	} else if terminal == yayaTerminal {
		switch category {
		case categoryRecreation:
			page = "首页"
		case categorySidebar:
			page = "房间内侧边栏"
		}
	}
	return page
}

// 获取默认推荐公式
func getDefaultFormula(params map[int]mgodao.FormulaParameter, zoneID int) (f mgodao.FormulaParameter) {
	var ok bool
	f, ok = params[zoneID]
	if !ok {
		f = mgodao.FormulaParameter{ZoneID: zoneID, X1: 1, X2: 1, Y1: 1, Y2: 1, Y3: 1, Y4: 1, Y5: 1, Y6: 1}
	}
	return
}

// SearchChPopular 搜索人气值
func SearchChPopular(c *gin.Context) {
	rw, req := c.Writer, c.Request
	var ret = struct {
		fts.RespHeader
		List []ChPopularDescribe `json:"list"`
	}{}
	defer util.HttpRespJson(rw, req, &ret)

	info := struct {
		UID      int64 `json:"uid"`
		SID      int64 `json:"sid"`
		SSID     int64 `json:"ssid"`
		Tab      int64 `json:"tab"`
		Terminal int64 `json:"terminal"`
	}{}

	info.UID = fts.NewRequest(req).GetInt64("uid", 0)
	info.SID = fts.NewRequest(req).GetInt64("sid", 0)
	info.SSID = fts.NewRequest(req).GetInt64("ssid", 0)
	info.Tab = fts.NewRequest(req).GetInt64("tab", 0)

	yylog.Info("", zap.Any("info", info))

	if info.SID == 0 && info.SSID == 0 && info.UID == 0 && info.Tab == 0 {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	var tabID []int64
	var tabIDAssociateTerminal []int
	var tabList []mgodao.RecommendTabConfig
	var err error
	tabList, err = mgodao.GetRecommendTabConfig(0, 0, 0)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	for _, tab := range tabList {
		if info.Tab == 0 {
			tabID = append(tabID, tab.ID)
			tabIDAssociateTerminal = append(tabIDAssociateTerminal, tab.Terminal)
		} else if tab.ID == info.Tab {
			tabID = append(tabID, info.Tab)
			tabIDAssociateTerminal = append(tabIDAssociateTerminal, tab.Terminal)
		}
	}

	var platform []int
	platform = append(platform, platformAndroid, platformIOS, platformPC)

	var vi []*viewItem
	for i, id := range tabID {
		for _, p := range platform {
			items, _, err := getRecommendInfo(c.Request.Context(), id, p, tabIDAssociateTerminal[i], fts.Version{}, -1)
			if err != nil {
				ret.SetStatus(fts.StatusAccessRedisError)
				return
			}
			for _, item := range items {
				// log.Debug("%+v %+v %+v", id, p, item)
				item.Platform = p
				vi = append(vi, item)
			}
		}
	}
	var retList []*viewItem
	channels := make(map[PopularityKey]int)
	var elemMap map[PopularityKey]fts_ch_popular.ChannelPopularityElemInfo
	for _, item := range vi {
		// uid筛选
		if info.UID > 0 && info.UID != item.UID {
			continue
		}

		// sid筛选
		if info.SID > 0 && info.SID != item.Sid {
			continue
		}

		// ssid筛选
		if info.SSID > 0 && info.SSID != item.Ssid {
			continue
		}

		if item.Sid > 0 || item.Ssid > 0 {
			c := PopularityKey{Sid: item.Sid, Ssid: item.Ssid, ZoneID: int(item.ZoneID - item.Position)}
			channels[c] = 1
		}
		retList = append(retList, item)
	}

	elemMap, err = getRoomPopularityValueElem(channels)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	var params map[int]mgodao.FormulaParameter
	params, err = mgodao.BatchGetFormulaParameter(tabID)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}

	retMap := make(map[ChInfoKey]ChPopularDescribe)
	zoneIDMap := make(map[int]int)
	for i := 0; i < len(retList); i++ {
		zoneID := retList[i].ZoneID - retList[i].Position
		terminal, platform, name, page := getTerminalInfo(zoneID)
		c := PopularityKey{
			Sid:    retList[i].Sid,
			Ssid:   retList[i].Ssid,
			ZoneID: int(zoneID),
		}
		k := ChInfoKey{
			UID:      retList[i].UID,
			Sid:      retList[i].Sid,
			Ssid:     retList[i].Ssid,
			Terminal: terminal,
		}
		v := ChPopularDescribe{
			UID:      retList[i].UID,
			Sid:      retList[i].Sid,
			Ssid:     retList[i].Ssid,
			Terminal: terminal,
			Title:    retList[i].Title,
		}
		l := ChPopularInfo{
			Page:              page,
			Tab:               name,
			ZoneID:            zoneID,
			ZoneName:          name,
			Position:          retList[i].Position,
			OnlineUser:        elemMap[c].RealCount,
			PublicScreenSpeak: elemMap[c].ChatNum,
			MicorderCnt:       elemMap[c].MicorderCnt,
			PropsFlow:         elemMap[c].Propsamount,
			LiveDuration:      elemMap[c].Liveduration,
			RobotCount:        elemMap[c].RobotUserNo,
			SeatAreaNum:       elemMap[c].SeatAreaNum,
			Formula:           getDefaultFormula(params, int(zoneID)),
			Popularity:        elemMap[c].Popularity,
			Platform:          platform,
			ActivePopularity:  elemMap[c].ActivePopularity,
			PropsPopularity:   elemMap[c].PropsPopularity,
		}

		value, ok := retMap[k]
		_, exist := zoneIDMap[int(zoneID)]
		if !ok {
			v.ChPopularInfoList = append(v.ChPopularInfoList, l)
			retMap[k] = v
			zoneIDMap[int(zoneID)] = 1
		} else {
			if !exist {
				zoneIDMap[int(zoneID)] = 1
				value.ChPopularInfoList = append(value.ChPopularInfoList, l)
				retMap[k] = value
			}

		}

	}
	for _, value := range retMap {
		ret.List = append(ret.List, value)
	}
}

// GetTabList Huoqu
func GetTabList(c *gin.Context) {
	rw, req := c.Writer, c.Request
	ret := struct {
		fts.RespHeader
		Items []any `json:"list"`
	}{}

	item := struct {
		TabID    int64  `json:"tabId"`
		Name     string `json:"name"`
		Terminal int    `json:"terminal"`
	}{}
	defer util.HttpRespJson(rw, req, &ret)

	recommendTabConfig.Range(func(k, v any) bool {
		if v, ok := v.(mgodao.RecommendTabConfig); ok {
			item.TabID = v.ID
			item.Name = v.Name
			item.Terminal = v.Terminal
			ret.Items = append(ret.Items, item)
		}
		return true
	})
}

// ================================= Debug 接口

// GetDebugInfo 获取数据用于问题排查
func GetDebugInfo(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		RecommendTabConfig []mgodao.RecommendTabConfig `json:"recommendTabConfig"`
		ViewItem           []*viewItem                 `json:"viewItem"`
	}{}

	id, _ := strconv.ParseInt(c.Query("id"), 10, 64)
	platform, _ := strconv.ParseInt(c.Query("platform"), 10, 64)

	var tabList []mgodao.RecommendTabConfig
	recommendTabConfig.Range(func(k, v any) bool {
		cfg, ok := v.(mgodao.RecommendTabConfig)
		if !ok {
			return true
		}
		tabList = append(tabList, cfg)
		return true
	})

	ret.RecommendTabConfig = tabList
	ret.ViewItem, _ = getRecommendViewItem(c.Request.Context(), id, int(platform))

	c.JSONP(http.StatusOK, ret)
}

// GetRecommendSnapshot 获取数据用于问题排查
func GetRecommendSnapshot(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		RecommendTabConfig []mgodao.RecommendTabConfig `json:"recommendTabConfig"`
		RecommendList      []*viewItem                 `json:"recommendList"`
		NotRecommendList   []*notRecommendItem         `json:"notRecommendList"`
	}{}

	ret.Msg = "platform: 1 -and 2 -ios 3 -and+ios 4 -pc 5 -pcweb 6 -pc+app"

	id, _ := strconv.ParseInt(c.Query("id"), 10, 64)
	platform, _ := strconv.ParseInt(c.Query("platform"), 10, 64)
	disabledMigrate, _ := strconv.ParseInt(c.Query("disabledMigrate"), 10, 64)

	var tabList []mgodao.RecommendTabConfig
	recommendTabConfig.Range(func(k, v any) bool {
		cfg, ok := v.(mgodao.RecommendTabConfig)
		if !ok {
			return true
		}
		tabList = append(tabList, cfg)
		return true
	})

	ret.RecommendTabConfig = tabList
	ret.RecommendList, _ = getRecommendViewItem(c.Request.Context(), id, int(platform))
	ret.NotRecommendList, _ = getNotRecommendItem(c.Request.Context(), id, int(platform), int(disabledMigrate))

	c.JSONP(http.StatusOK, ret)
}

// OpGetUserEnterChannelInfo 用户进频道查询
func OpGetUserEnterChannelInfo(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		List []mgodao.UserEnterChannelInfo `json:"list"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	var req opGetUserEnterChannelInfoQuery
	if err := c.ShouldBindQuery(&req); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	records, err := mgodao.GetRecentlyEntryChannelInfo(req.UID, 1000)
	if err != nil {
		ret.SetStatus(fts.StatusAccessDBError)
		return
	}
	ret.List = records
}
