package v2

import (
	"context"
	"encoding/json"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/privilegedao"
	"math/rand"
	"net/http"
	"sort"
	"sync"
	"time"

	"git.yy.com/golang/gfy/v2/gfy"

	"git.yy.com/golang/gfy/v2/yy/yylog"
	"go.uber.org/zap"

	"github.com/gin-gonic/gin"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/zhuiwan_channel"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/videodatingdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/culifdao"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_video_dating"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_game_center"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/webdbdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/channelfightdao"
	"git.yy.com/server/jiaoyou/go_projects/api/basedao/videofightdao"

	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_channel_fight"
	"git.yy.com/server/jiaoyou/go_projects/api/gen-go/fts_video_fight"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/gamecenterdao"

	"git.yy.com/server/jiaoyou/go_projects/api/basedao/zhuiwandao"

	"git.yy.com/server/jiaoyou/go_projects/api/common/constinfo"

	"git.yy.com/server/jiaoyou/go_projects/api/common/fts"
	"git.yy.com/server/jiaoyou/go_projects/api/common/util"

	mgodao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/mgodao/v2"
	redisdao "git.yy.com/server/jiaoyou/go_projects/fts_zhuiya_recommend/dao/redisdao/v2"
)

// 推荐模块
type recommendModule struct {
	Title    string      `json:"title"`
	Position int         `json:"position"`
	Items    []*viewItem `json:"list"`
}

// 推荐信息
type recommendInfoResp struct {
	fts.RespHeader
	Items   []*viewItem        `json:"list"`
	Module  []*recommendModule `json:"module"`
	Banner  []bannerInfo       `json:"banner"`
	Next    int                `json:"next"`
	Version int                `json:"version"` // 返回数据版本，0-为旧版，1-为新版
}

type tabInfoReq struct {
	Category int    `form:"category"`
	Platform int    `form:"platform"`
	HostName string `form:"hostName"` // debug
	Version  string `form:"version"`  // debug
}

type tabInfoResp struct {
	fts.RespHeader
	Items []*recommendTab `json:"list"`
	Pos   int             `json:"pos"` // 定位到哪个导航栏，0-表示第1个、1-表示第2个
}

type recommendInfoReq struct {
	ID              int64  `form:"id"`
	Category        int    `form:"category"`
	Platform        int    `form:"platform"` // 调试参数，请求头拿不到 x-fts-platform 就会用这个
	Next            int    `form:"next" binding:"gte=0"`
	HostName        string `form:"hostName"`        // 调试参数，请求头拿不到 x-fts-host-name 就会用这个
	Version         string `form:"version"`         // 调试参数，请求头拿不到 x-fts-host-version 就会用这个
	UID             int64  `form:"uid"`             // 模拟uid，优先读取登录态的没有才会用这个
	DisabledMigrate int    `form:"disabledMigrate"` // 是否禁用迁移结果，0-启用 1-禁用 2-强制使用迁移结果
}

type getChannelFightVsInfoReq struct {
	Sid  int64 `form:"sid" binding:"gt=0"`
	Ssid int64 `form:"ssid" binding:"gt=0"`
}

type getChannelFightVsInfoResp struct {
	fts.RespHeader
	UID    int64  `json:"uid"`    // 嘉宾uid
	Nick   string `json:"nick"`   // 昵称
	Avatar string `json:"avatar"` // 头像
}

type getRecommendExtInfoResp struct {
	fts.RespHeader
	Interval int         `json:"interval"` // 轮询时间间隔
	Size     int         `json:"size"`     // 查询的频道个数
	Items    []*viewItem `json:"list"`
}

// 优先使用客户端header里面带的platform
// 如果header里面没有带platform使用参数上带的platform
func adapterPlatform(platform int, mark *fts.HTTPHeaderMark) int {
	var compatiblePlatform int
	switch {
	case mark.IsPlatformAndroid():
		compatiblePlatform = platformAndroid
	case mark.IsPlatformIOS():
		compatiblePlatform = platformIOS
	case mark.IsPlatformPC():
		compatiblePlatform = platformPC
	default:
		switch platform {
		case 1, 3: // android
			compatiblePlatform = platformAndroid
		case 2, 4: // ios
			compatiblePlatform = platformIOS
		case 5:
			compatiblePlatform = platformPC
		}
	}
	return compatiblePlatform
}

func isRecommendTab(config *mgodao.RecommendTabConfig, query *tabInfoReq, terminal int, v fts.Version) bool {
	if !config.IsShowStatus() {
		return false
	}
	hide, ok := lessShowMinSizeTab.Load(lessShowMinSizeTabKey(config.ID, query.Platform))
	if !ok {
		return false
	}
	if value, ok := hide.(bool); value && ok {
		return false
	}
	if config.Category != query.Category {
		return false
	}
	// yo和追玩共用一个娱乐页面
	if isYoTerminal(terminal) && (query.Category == categoryRecreation || query.Category == categoryInsertModule) {
		terminal = zwTerminal
	}
	if config.Terminal != terminal {
		return false
	}
	yylog.Debug("recommend tab", zap.Any("query", query), zap.Int("terminal", terminal), zap.Any("config", config),
		zap.String("version", v.Format()))
	if !config.ShowThisVersion(v.Major, v.Minor, v.Patch) {
		return false
	}
	if config.Platform != query.Platform &&
		config.Platform != platformAndroidAndIOS &&
		config.Platform != platformPCAndAPP {
		return false
	}
	return true
}

// GetTabInfo 导航配置
// @Summary 导航配置
// @Description 导航配置
// @Tags 导航配置
// @Accept json
// @Produce json
// @Param category query int true "1-娱乐、2-游戏、3-发现页、4-分流推荐、5-云游戏、6-游戏专区、7-聊天室、9-侧边栏、10-H5语音房"
// @Param x-fts-platform header int true "1-PC、3-android、4-ios"
// @Success 200 {object} tabInfoResp
// @Router /get_tab_info [get]
func GetTabInfo(c *gin.Context) {
	var ret tabInfoResp
	defer c.JSON(http.StatusOK, &ret)

	var query tabInfoReq
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	mark := fts.NewHTTPHeaderMark(c.Request.Header)
	if len(mark.HostVersion) == 0 {
		mark.HostVersion = query.Version
	}
	if len(mark.HostName) == 0 {
		mark.HostName = query.HostName
	}

	query.Platform = adapterPlatform(query.Platform, mark)
	if query.Platform == 0 {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	version := mark.GetHostVersion()

	// 从缓存中获取符合条件的列表
	recommendTabConfig.Range(func(k, v any) bool {
		config, ok := v.(mgodao.RecommendTabConfig)
		if !ok {
			return true
		}
		if !isRecommendTab(&config, &query, appTerminal(mark), version) {
			return true
		}
		ret.Items = append(ret.Items, &recommendTab{
			ID:       config.ID,
			Name:     config.Name,
			Weight:   config.Weight,
			Style:    config.Style,
			RoomType: config.RoomType,
		})
		return true
	})
	sort.Slice(ret.Items, func(i, j int) bool {
		return ret.Items[i].Weight > ret.Items[j].Weight
	})
	for i := range ret.Items {
		if len(ret.Items[i].RoomType) > 0 && isRecommendRoomType(mark.RoomType, ret.Items[i].RoomType) {
			ret.Pos = i
			break
		}
	}
	yylog.Info("tab info", zap.Any("query", query), zap.String("version", version.Format()),
		zap.Int("itemSize", len(ret.Items)))
}

func adapterMayLikeJYID(id int64, mark *fts.HTTPHeaderMark) int64 {
	switch {
	case mark.IsYoHostName():
		return 2100
	case mark.IsYoKHHostName():
		return 3100
	case mark.IsYaYaHostName():
		return 5100
	default:
	}
	return id
}

func adapterMayLikePKID(id int64, mark *fts.HTTPHeaderMark) int64 {
	switch {
	case mark.IsYoHostName():
		return 2200
	case mark.IsYoKHHostName():
		return 3200
	case mark.IsYaYaHostName():
		return 5200
	default:
	}
	return id
}

func adapterMayLikeBaByID(id int64, mark *fts.HTTPHeaderMark) int64 {
	switch {
	case mark.IsYoHostName():
		return 2600
	case mark.IsYoKHHostName():
		return 3600
	case mark.IsYaYaHostName():
		return 5600
	default:
	}
	return id
}

func adapterMayLikeVoiceRoomID(id int64, mark *fts.HTTPHeaderMark) int64 {
	switch {
	case mark.IsYoHostName():
		return 2400
	case mark.IsYoKHHostName():
		return 3400
	case mark.IsYaYaHostName():
		return 5400
	default:
	}
	return id
}

func adapterMayLikeGameTeam(id int64, mark *fts.HTTPHeaderMark) int64 {
	switch {
	case mark.IsYoHostName():
		return 2500
	case mark.IsYoKHHostName():
		return 3500
	case mark.IsYaYaHostName():
		return 5500
	default:
	}
	return id
}

func adapterSearchRecommmendRoom(id int64, mark *fts.HTTPHeaderMark) int64 {
	if mark.IsYoHostName() {
		return id
	}
	return 8500
}

func adapterMoreExciting(id int64, mark *fts.HTTPHeaderMark) int64 {
	// 语音房、基础&不支持模板的更多精彩显示为互动秀-更多精彩补量内容
	if !mark.IsYoJYHostName() && !mark.IsBusinessJY() && !mark.IsBusinessBaby() {
		return id
	}
	return 8100
}

func adapterExitRoom(id int64, mark *fts.HTTPHeaderMark) int64 {
	// 语音房、基础&不支持模板的更多精彩显示为互动秀-更多精彩补量内容
	if !mark.IsYoJYHostName() && !mark.IsBusinessJY() && !mark.IsBusinessBaby() {
		return id
	}
	return 8300
}

func adapterID(id int64, mark *fts.HTTPHeaderMark) int64 {
	switch id {
	case 300: // 上下滑动
		version := mark.GetHostVersion()
		if util.CompareVersion(version.Format(), "1.15.0") == -1 {
			return id
		}
		if !mark.IsBusinessJY() && !mark.IsBusinessBaby() {
			return 2300
		}
	case 1800: // 上下滑补充-娱乐房
		if !mark.IsBusinessJY() && !mark.IsBusinessBaby() {
			return 1900 // 上下滑补充-技能卡
		}
	case 1100: // 猜你喜欢-交友房
		return adapterMayLikeJYID(id, mark)
	case 1200: // 猜你喜欢-约战房
		return adapterMayLikePKID(id, mark)
	case 1300: // 猜你喜欢-宝贝房
		return adapterMayLikeBaByID(id, mark)
	case 1400: // 猜你喜欢-语音房
		return adapterMayLikeVoiceRoomID(id, mark)
	case 1500: // 猜你喜欢-YY频道
		return adapterMayLikeGameTeam(id, mark)
	case 8000: // 更多精彩
		return adapterMoreExciting(id, mark)
	case 8200: // 语音房-退出房间
		return adapterExitRoom(id, mark)
	case 8400: // 搜索推荐房间
		return adapterSearchRecommmendRoom(id, mark)
	}
	return id
}

// GetRecommendInfo 获取推荐数据
// GetTabInfo 获取推荐数据
// @Summary 获取推荐数据
// @Description 获取推荐数据
// @Tags 推荐数据
// @Accept json
// @Produce json
// @Param id query int true "导航栏tab id"
// @Param x-fts-platform header int true "1-PC、3-android、4-ios"
// @Success 200 {object} recommendInfoResp
// @Router /get_recommend_info [get]
func GetRecommendInfo(c *gin.Context) {
	var query recommendInfoReq
	if err := c.ShouldBindQuery(&query); err != nil {
		var ret recommendInfoResp
		ret.SetStatus(fts.StatusBadRequest)
		c.JSON(http.StatusOK, &ret)
		return
	}
	ctx := c.Request.Context()

	mark := fts.NewHTTPHeaderMark(c.Request.Header)
	if len(mark.HostName) == 0 {
		// 优先使用 header 中的
		mark.HostName = query.HostName
	}
	var adaptedPlatform = adapterPlatform(query.Platform, mark)
	if adaptedPlatform == 0 {
		var ret recommendInfoResp
		ret.SetStatus(fts.StatusBadRequest)
		c.JSON(http.StatusOK, &ret)
		return
	}

	var adaptedTabID = adapterID(query.ID, mark)
	// 已迁移, 去hd-recommend 中查询
	if query.DisabledMigrate == 2 || (query.DisabledMigrate != 1 && migrate.isMigrate(adaptedTabID)) {
		migrate.getRecommendInfo(c, &query)
		return
	}

	var ret recommendInfoResp
	defer c.JSON(http.StatusOK, &ret)

	query.ID = adaptedTabID
	query.Platform = adaptedPlatform
	terminal := appTerminal(mark)
	if len(mark.HostVersion) <= 0 {
		mark.HostVersion = query.Version
	}
	version := mark.GetHostVersion()

	uid := getCookieUID(c)
	now := time.Now()
	wg := util.WaitGroupWrapper{}
	wg.Wrap(func() {
		// 获取推荐信息
		var err error
		ret.Items, ret.Next, err = getRecommendInfo(ctx, query.ID, query.Platform, terminal, version, query.Next)
		if err != nil {
			yylog.ErrorWithBam("get recommend info", fts.TraceID(ctx),
				fts.UID(uid), zap.Error(err), zap.Int64("id", query.ID),
				zap.Int("terminal", terminal), zap.Int("platform", query.Platform))
			return
		}
	}).Wrap(func() {
		// 模块信息
		recommendTabConfig.Range(func(k, v any) bool {
			config, ok := v.(mgodao.RecommendTabConfig)
			if !ok {
				return true
			}
			if config.InsertedTab.ID != query.ID {
				return true
			}
			tabInfoQuery := tabInfoReq{
				Category: categoryInsertModule,
				Platform: query.Platform,
			}
			if !isRecommendTab(&config, &tabInfoQuery, terminal, version) {
				return true
			}
			items, _, err := getRecommendInfo(ctx, config.ID, query.Platform, terminal, version, 0)
			if err != nil {
				yylog.ErrorWithBam("cannot get recommend info", fts.TraceID(ctx),
					fts.UID(uid), zap.Error(err), zap.Int64("id", config.ID),
					zap.Int("terminal", terminal), zap.Int("platform", query.Platform))
				return true
			}
			yylog.Info("get recommend module info", fts.TraceID(ctx),
				fts.UID(uid), zap.Int64("id", config.ID), zap.Int("terminal", terminal),
				zap.Int("platform", query.Platform), zap.String("version", version.Format()), zap.Int("moduleSize", len(ret.Module)))
			ret.Module = append(ret.Module, &recommendModule{
				Title:    config.Name,
				Position: config.InsertedTab.Position,
				Items:    items,
			})
			return true
		})
		if len(ret.Module) > 0 {
			sort.Slice(ret.Module, func(i, j int) bool {
				return ret.Module[i].Position < ret.Module[j].Position
			})
		}
	}).Wrap(func() {
		// 获取banner
		categoryRecreationMap := make(map[int64]bool)
		recommendTabConfig.Range(func(k, v any) bool {
			config, ok := v.(mgodao.RecommendTabConfig)
			if !ok {
				return true
			}

			if config.Category == categoryRecreation {
				categoryRecreationMap[config.ID] = true
			}
			return true
		})
		// yo和追玩共用一个娱乐页面
		if isYoTerminal(terminal) && categoryRecreationMap[query.ID] {
			mark.HostName = "dreamer"
		}
		businessTmp := hostNameToBusiness(mark)
		platformTmp := platformHeaderToPlatform(mark)
		banner, err := GetBannerData(ctx, businessTmp, platformTmp, 0, query.ID, bannerPosTypeNotTop, version.Format())
		if err != nil {
			yylog.Error("cannot get banner data", fts.TraceID(ctx),
				fts.UID(uid), zap.Error(err), zap.Int64("id", query.ID),
				zap.Int64("business", businessTmp), zap.Int("platform", query.Platform))
			return
		}
		ret.Banner = banner
	}).Wait()
	yylog.Info("recommend info", fts.TraceID(ctx),
		fts.UID(uid), zap.Int64("id", query.ID), zap.Int("terminal", terminal), zap.Int("platform", query.Platform),
		zap.String("version", version.Format()), zap.Int("itemSize", len(ret.Items)), zap.Int("bannerSize", len(ret.Banner)),
		zap.Int("next", query.Next), zap.Any("timeElapsed", time.Since(now)))
}

// GetGameSearchItem 端游开黑搜索框列表
func GetGameSearchItem(c *gin.Context) {
	type searchItem struct {
		GameID   int    `json:"gameId"`   // 游戏gameId
		GameName string `json:"gameName"` // 游戏名称
	}

	ret := struct {
		fts.RespHeader
		Items []*searchItem `json:"list"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	query := struct {
		ID       int64 `form:"id"`
		Platform int   `form:"platform"`
	}{}
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	mark := fts.NewHTTPHeaderMark(c.Request.Header)
	query.Platform = adapterPlatform(query.Platform, mark)
	if query.Platform == 0 {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	items, _, err := getRecommendInfo(c.Request.Context(), query.ID, query.Platform, zwTerminal, mark.GetHostVersion(), -1)
	if err != nil {
		yylog.Error("cannot get recommend info", fts.TraceID(c.Request.Context()),
			zap.Error(err), zap.Int64("id", query.ID), zap.Int("platform", query.Platform))
		return
	}
	seen := make(map[string]bool)
	for _, item := range items {
		if seen[item.GameName] {
			continue
		}
		seen[item.GameName] = true
		ret.Items = append(ret.Items, &searchItem{
			GameID:   item.GameID,
			GameName: item.GameName,
		})
	}
	yylog.Debug("recommend info", fts.TraceID(c.Request.Context()), zap.Any("ret", ret))
	return
}

// GetChannelLiveAndLockStatus 频道的开播和上锁状态
func GetChannelLiveAndLockStatus(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		Live bool `json:"live"`
		Lock bool `json:"lock"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	query := struct {
		Sid  int64 `form:"sid" binding:"gt=0"`
		Ssid int64 `form:"ssid" binding:"gt=0"`
	}{}
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	channel := Channel{Sid: query.Sid, Ssid: query.Ssid}
	if m := batchSubChannelPasswordMapInfo([]Channel{channel}); m != nil {
		ret.Lock = m[channel]
	}
	if !ret.Lock {
		var err error
		ret.Lock, err = zhuiwandao.CheckZhuiwanChannelIsLock(query.Sid, query.Ssid)
		if err != nil {
			yylog.Error("cannot check zw channel is lock", zap.Error(err), fts.SSID(query.Sid), fts.SID(query.Ssid))
		}
	}

	if value, ok := onlineChannelCompere.Load(channel); ok {
		if uid, ok := value.(int64); ok && uid > 0 {
			yylog.Error("online info", fts.UID(uid), fts.SSID(query.Sid), fts.SID(query.Ssid))
			ret.Live = true
		}
	}
	yylog.Info("lock status", fts.SSID(query.Sid), fts.SID(query.Ssid), zap.Any("ret", ret))
}

// GetGameIDRecommendInfo 云游戏gameId推荐接口
func GetGameIDRecommendInfo(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		Items []*viewItem `json:"list"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	query := struct {
		GameID   int `form:"gameId"`
		Platform int `form:"platform"`
	}{}
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	mark := fts.NewHTTPHeaderMark(c.Request.Header)
	query.Platform = adapterPlatform(query.Platform, mark)
	if query.Platform == 0 {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	now := time.Now()
	var wg sync.WaitGroup
	recommendTabConfig.Range(func(k, v any) bool {
		config, ok := v.(mgodao.RecommendTabConfig)
		if !ok {
			return true
		}
		if config.Category != categoryCloudGame {
			return true
		}
		if !isAPPTerminal(config.Terminal) {
			return true
		}
		terminal := appTerminal(mark)
		if config.Terminal != terminal {
			return true
		}
		wg.Add(1)

		gfy.Go(c.Request.Context(), func(ctx context.Context) int {
			func(configID int64, platform int) {
				defer wg.Done()
				items, err := getRecommendViewItem(ctx, configID, platform)
				if err != nil {
					yylog.Error("cannot get recommend view item", fts.TraceID(ctx), zap.Error(err), zap.Int64("configId", configID),
						zap.Int("platform", platform))
					return
				}
				var mutex sync.Mutex
				for _, item := range items {
					if item.GameID == query.GameID {
						mutex.Lock()
						ret.Items = append(ret.Items, item)
						mutex.Unlock()
					}
					yylog.Debug("recommend item", fts.TraceID(ctx), zap.Int64("configId", configID), zap.Int("platform", platform),
						zap.Int("gameId", query.GameID), zap.Any("item", item))
				}
			}(config.ID, query.Platform)
			return 0
		})
		return true
	})
	wg.Wait()
	yylog.Info("recommend info", fts.TraceID(c.Request.Context()), zap.Int("gameId", query.GameID), zap.Int("platform", query.Platform),
		zap.Int("itemSize", len(ret.Items)), zap.Any("timeElapsed", time.Since(now)))
}

// GetRandomOnlineRoom 返回一个随机开播频道的接口
func GetRandomOnlineRoom(c *gin.Context) {
	ret := struct {
		fts.RespHeader
		viewItem
		OnlineUsers int64 `json:"onlineUsers"`
	}{}
	defer c.JSON(http.StatusOK, &ret)

	query := struct {
		ID       int64 `form:"id"`
		Platform int   `form:"platform"`
	}{}
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	mark := fts.NewHTTPHeaderMark(c.Request.Header)
	query.Platform = adapterPlatform(query.Platform, mark)
	if query.Platform == 0 {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	next := -1
	now := time.Now()
	// 获取推荐信息
	items, _, err := getRecommendInfo(c.Request.Context(), query.ID, query.Platform, zwTerminal, mark.GetHostVersion(), next)
	if err != nil || len(items) <= 0 {
		yylog.ErrorWithBam("cannot get recommend info", fts.TraceID(c.Request.Context()),
			zap.Error(err), zap.Int64("id", query.ID), zap.Int("platform", query.Platform))
		return
	}

	item := items[rand.Intn(len(items))]
	ret.UID = item.UID
	ret.Sex = item.Sex
	ret.Sid = item.Sid
	ret.Ssid = item.Ssid
	ret.Psu = item.Psu
	ret.Title = item.Title
	ret.Nick = item.Nick
	ret.Cover = item.Cover

	count, err := culifdao.QuerySubChannelUserCount(item.Sid, item.Ssid)
	if err != nil {
		yylog.Warn("get channel online users", fts.TraceID(c.Request.Context()),
			fts.SSID(item.Sid), fts.SID(item.Ssid), zap.Error(err))
	}
	ret.OnlineUsers = count
	yylog.Info("recommend info", fts.TraceID(c.Request.Context()),
		zap.Int64("id", query.ID), zap.Int("platform", query.Platform),
		zap.Any("item", item), zap.Any("timeElapsed", time.Since(now)))
}

func channelListCategory(channelList []*Channel,
	gameInfo map[fts_game_center.ChInfo]*fts_game_center.GameInfo,
	templateIDMap map[string]int64) (
	channelFightList []*fts_channel_fight.Channel,
	videoFightChannelList []*fts_video_fight.VideoFightCh,
	videoDatingChannelList []*fts_video_dating.ChInfo,
	zwChannel []Channel) {
	for _, channel := range channelList {
		templateID, _ := templateIDMap[toChannelStr(channel.Sid, channel.Ssid)]
		if templateID != getTemplateID(businessJY) {
			if templateID != getTemplateID(businessBaby) {
				zwChannel = append(zwChannel, *channel)
			}
			continue
		}
		info, ok := gameInfo[*channel]
		if !ok {
			continue
		}
		if info == nil {
			continue
		}
		gameType := info.GetGameType()

		if constinfo.KChannelFight == int(gameType) {
			channelFightList = append(channelFightList, &fts_channel_fight.Channel{
				Sid:  channel.Sid,
				Ssid: channel.Ssid,
			})
		} else if constinfo.KVideoFightMatch == int(gameType) {
			videoFightChannelList = append(videoFightChannelList, &fts_video_fight.VideoFightCh{
				Sid:  channel.Sid,
				Ssid: channel.Ssid,
			})
		} else if gameType == constinfo.KVideoDating || gameType == constinfo.KVideoPk {
			videoDatingChannelList = append(videoDatingChannelList, &fts_video_dating.ChInfo{
				Sid:  channel.Sid,
				Ssid: channel.Ssid,
			})
		}
	}
	return
}

// deprecated see batchGetVsAndGuestInfo
func getVsAndGuestInfo(
	channelFightList []*fts_channel_fight.Channel,
	videoFightChannelList []*fts_video_fight.VideoFightCh,
	videoDatingChannelList []*fts_video_dating.ChInfo) (
	vdGuest, vfGuest map[Channel][]int64,
	cfVsMap, vfVsMap map[Channel]match,
	err error) {
	vdGuest = make(map[Channel][]int64)
	vfGuest = make(map[Channel][]int64)
	cfVsMap = make(map[Channel]match)
	vfVsMap = make(map[Channel]match)

	var wg util.WaitGroupWrapper
	wg.Wrap(func() {
		// 乱斗本方、对方信息
		if len(channelFightList) == 0 {
			return
		}
		var cfRet *fts_channel_fight.BatchChannelFightVsInfoRet
		cfRet, err = channelfightdao.BatchGetChannelFightVsInfo(channelFightList)
		if err != nil || cfRet == nil || cfRet.Ret != 0 {
			yylog.Error("cannot batch get channel fight vs info", zap.Any("ret", cfRet), zap.Error(err))
			return
		}
		for _, info := range cfRet.Info {
			c := Channel{Sid: info.Sid, Ssid: info.Ssid}
			cfVsMap[c] = match{Sid: info.VsSid, Ssid: info.VsSsid, UID: info.VsCompere}
		}
	}).Wrap(func() {
		// 多人乱斗本方、对方信息
		if len(videoFightChannelList) == 0 {
			return
		}
		var vfRet *fts_video_fight.BatchGetVideoFightInfoResp
		vfRet, err = videofightdao.BatchGetVideoFightInfo(&fts_video_fight.BatchGetVideoFightInfoReq{Chs: videoFightChannelList})
		if err != nil || vfRet == nil || vfRet.Ret != 0 {
			yylog.Error("cannot batch get video fight info", zap.Any("ret", vfRet), zap.Error(err))
			return
		}
		var allVideoFightChannelList []*fts_video_fight.VideoFightCh
		allVideoFightChannelList = append(allVideoFightChannelList, videoFightChannelList...)
		for _, info := range vfRet.Info {
			c := Channel{Sid: info.Sid, Ssid: info.Ssid}
			vfVsMap[c] = match{Sid: info.MatchSid, Ssid: info.MatchSsid, UID: info.MatchUid}
			allVideoFightChannelList = append(allVideoFightChannelList, &fts_video_fight.VideoFightCh{
				Sid:  info.MatchSid,
				Ssid: info.MatchSsid,
			})
		}

		var vfGuestRet *fts_video_fight.BatchGetVideoFightGuestCharmResp

		// 多人乱斗嘉宾
		vfGuestRet, err = videofightdao.BatchGetVideoFightGuestCharm(&fts_video_fight.BatchGetVideoFightGuestCharmReq{Chs: allVideoFightChannelList})
		if err != nil || vfGuestRet == nil || vfGuestRet.Ret != 0 {
			yylog.Error("cannot batch get video fight guest charm", zap.Any("ret", vfGuestRet), zap.Error(err))
			return
		}
		if len(vfGuestRet.ChGuests) != len(allVideoFightChannelList) {
			return
		}
		for i, info := range vfGuestRet.ChGuests {
			c := Channel{Sid: allVideoFightChannelList[i].Sid, Ssid: allVideoFightChannelList[i].Ssid}
			vfGuest[c] = make([]int64, 0, len(info))
			for _, g := range info {
				vfGuest[c] = append(vfGuest[c], g.UID)
			}
		}
	}).Wrap(func() {
		// 多人视频嘉宾
		if len(videoDatingChannelList) == 0 {
			return
		}
		var vdGuestRet *fts_video_dating.BatchGetVideoDatingInfoRsp
		vdGuestRet, err = videodatingdao.BatchGetVideoDatingInfo(videoDatingChannelList)
		if err != nil || vdGuestRet == nil || vdGuestRet.Resp == nil || vdGuestRet.Resp.Ret != 0 {
			yylog.Error("cannot batch get video dating info", zap.Any("ret", vdGuestRet), zap.Error(err))
			return
		}
		for _, info := range vdGuestRet.InfoList {
			if info == nil {
				continue
			}
			c := Channel{Sid: info.Sid, Ssid: info.Ssid}
			vdGuest[c] = make([]int64, 0, len(info.GetGuestList()))
			for _, g := range info.GetGuestList() {
				vdGuest[c] = append(vdGuest[c], g.UID)
			}
		}
	}).Wait()
	return
}

// GetRecommendExtInfo 更新推荐携带信息
// GetTabInfo 更新推荐携带信息
// @Summary 更新推荐携带信息
// @Description 更新推荐携带信息
// @Tags 推荐数据
// @Accept json
// @Produce json
// @Param channel body string true "channel=频道json列表"
// @Success 200 {object} getRecommendExtInfoResp
// @Router /get_recommend_ext_info [get]
func GetRecommendExtInfo(c *gin.Context) {
	var ret getRecommendExtInfoResp
	defer c.JSON(http.StatusOK, &ret)
	var ctx = c.Request.Context()

	now := time.Now()
	var channelList []*Channel
	err := json.Unmarshal([]byte(c.PostForm("channel")), &channelList)
	if err != nil {
		ret.SetStatus(fts.StatusJSONParseError)
		return
	}
	yylog.Debug("query channel", fts.TraceID(ctx), zap.Any("list", channelList))

	var wg util.WaitGroupWrapper
	var gameInfo map[fts_game_center.ChInfo]*fts_game_center.GameInfo
	var templateIDMap map[string]int64
	var stream map[uint64]string
	var mixStream map[string]string
	var passwordMap map[Channel]bool
	wg.Wrap(func() {
		// 交友玩法信息
		gameInfo, err = gamecenterdao.BatchGetGameInfoMap(channelList)
		if err != nil {
			yylog.Error("cannot batch get game info", fts.TraceID(ctx), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		// 模板id
		var channelStr []string
		for _, c := range channelList {
			channelStr = append(channelStr, toChannelStr(c.Sid, c.Ssid))
		}
		templateIDMap, err = webdbdao.BatchGetChannelTemplateID(channelStr)
		if err != nil {
			yylog.Error("cannot batch get channel template id", fts.TraceID(ctx), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		var ssidList []uint64
		var channelStrList []string
		for _, c := range channelList {
			ssidList = append(ssidList, uint64(c.Ssid))
			channelStrList = append(channelStrList, toChannelStr(c.Sid, c.Ssid))
		}
		stream, err = batchGetStreamInfo(ssidList)
		if err != nil {
			yylog.Error("cannot batch get stream info", fts.TraceID(ctx), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
		mixStream, err = redisdao.BatchGetLiveSpeed(channelStrList)
		if err != nil {
			yylog.Error("cannot batch get live speed", fts.TraceID(ctx), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		var channels []Channel
		for _, c := range channelList {
			channels = append(channels, *c)
		}
		passwordMap = batchSubChannelPasswordMapInfo(channels)
	}).Wait()

	_, _, _, zwChannel := channelListCategory(channelList, gameInfo, templateIDMap)

	var vdGuest, vfGuest map[Channel][]int64
	var cfVsMap, vfVsMap map[Channel]match
	babyOnlineInfoMap := make(map[string]int64)
	var zwChannelInfoMap map[Channel]*zhuiwan_channel.ChannelInfo
	wg.Wrap(func() {
		vdGuest, vfGuest, cfVsMap, vfVsMap, err = batchGetVsAndGuestInfo(c.Request.Context(), channelList, gameInfo, templateIDMap)
		if err != nil {
			yylog.Error("cannot get vs and guest info", fts.TraceID(ctx), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		// 追玩频道信息
		zwChannelInfoMap, err = batchGetSkillChannelInfo(zwChannel)
		if err != nil {
			yylog.Error("cannot get get skill channel info", fts.TraceID(ctx), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		babyOnlineInfoMap, err = redisdao.GetBabyOnlineInfo()
		if err != nil {
			yylog.Error("cannot get baby online info", fts.TraceID(ctx), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wait()

	for _, channel := range channelList {
		c := *channel
		var item viewItem
		item.Sid = c.Sid
		item.Ssid = c.Ssid
		item.TemplateID, _ = templateIDMap[toChannelStr(c.Sid, c.Ssid)]
		item.LiveInfo, _ = stream[uint64(c.Ssid)]
		zwChannelInfo := zwChannelInfoMap[c]
		item.MixStream, _ = mixStream[toChannelStr(c.Sid, c.Ssid)]
		var pluginType int32
		if zwChannelInfo != nil {
			pluginType = zwChannelInfoMap[c].PluginType
		}
		item.Business = getBusiness(item.TemplateID, pluginType)
		item.Lock, _ = passwordMap[c]
		switch item.Business {
		case businessJY:
			info, _ := gameInfo[c]
			if info != nil {
				item.GameType = int(info.GetGameType())
				item.UID = info.CompereUid
				var vs match
				switch item.GameType {
				case constinfo.KChannelFight:
					vs, _ = cfVsMap[c]
				case constinfo.KVideoFightMatch, constinfo.KVideoFightMatchNew:
					vs, _ = vfVsMap[c]
					item.GuestUID = vfGuest[c]
					item.Match.GuestUID = vfGuest[Channel{Sid: vs.Sid, Ssid: vs.Ssid}]
				case constinfo.KVideoDating, constinfo.KVideoPk, constinfo.KVideoDatingNew, constinfo.KVideoPkNew:
					item.GuestUID = vdGuest[c]
				}
				item.Match.Sid = vs.Sid
				item.Match.Ssid = vs.Ssid
				item.Match.UID = vs.UID
			}
		case businessBaby:
			item.UID, _ = babyOnlineInfoMap[toChannelStr(item.Sid, item.Ssid)]
		default:
			if zwChannelInfo != nil {
				item.RoomTag = zwChannelInfo.RoomTag
				item.Lock = zwChannelInfo.IsLocked == 1
				item.Background = zwChannelInfo.Background
				item.BackgroundNew = zwChannelInfo.BackgroundNew
				item.UID = zwChannelInfo.Ow
				item.RoomType = int(zwChannelInfo.RoomTypeId)
				item.RoomClass = int(zwChannelInfo.RoomClass)
			}
		}
		yylog.Debug("recommend info", fts.TraceID(ctx), fts.SID(item.Sid), fts.SSID(item.Ssid),
			zap.Any("item", item), zap.Any("zwChannelInfo", zwChannelInfo))
		ret.Items = append(ret.Items, &item)
	}
	ret.Interval = 10
	ret.Size = 20
	yylog.Info("recommend info", fts.TraceID(ctx), zap.Int("itemSize", len(ret.Items)), zap.Any("timeElapsed", time.Since(now)))
}

// GetRecommendExtInfoV2 更新推荐携带信息
// GetTabInfo 更新推荐携带信息
// @Summary 更新推荐携带信息
// @Description 更新推荐携带信息
// @Tags 推荐数据
// @Accept json
// @Produce json
// @Param channel body string true "channel=频道json列表"
// @Success 200 {object} getRecommendExtInfoResp
// @Router /get_recommend_ext_info [get]
func GetRecommendExtInfoV2(c *gin.Context) {
	var ret getRecommendExtInfoResp
	defer c.JSON(http.StatusOK, &ret)
	var ctx = c.Request.Context()

	now := time.Now()
	var channelList []*Channel
	err := json.Unmarshal([]byte(c.PostForm("channel")), &channelList)
	if err != nil {
		ret.SetStatus(fts.StatusJSONParseError)
		return
	}
	uid := getCookieUID(c)
	yylog.Debug("query channel", fts.TraceID(ctx), fts.UID(uid), zap.Any("list", channelList))

	var wg util.WaitGroupWrapper
	var gameInfo map[fts_game_center.ChInfo]*fts_game_center.GameInfo
	var templateIDMap map[string]int64
	var stream map[uint64]string
	var mixStream map[string]string
	var passwordMap map[Channel]bool
	wg.Wrap(func() {
		// 交友玩法信息
		gameInfo, err = gamecenterdao.BatchGetGameInfoMap(channelList)
		if err != nil {
			yylog.Error("cannot batch get game info", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		// 模板id
		var channelStr []string
		for _, c := range channelList {
			channelStr = append(channelStr, toChannelStr(c.Sid, c.Ssid))
		}
		templateIDMap, err = webdbdao.BatchGetChannelTemplateID(channelStr)
		if err != nil {
			yylog.Error("cannot batch get channel template id", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		var ssidList []uint64
		var channelStrList []string
		for _, c := range channelList {
			ssidList = append(ssidList, uint64(c.Ssid))
			channelStrList = append(channelStrList, toChannelStr(c.Sid, c.Ssid))
		}
		stream, err = batchGetStreamInfo(ssidList)
		if err != nil {
			yylog.Error("cannot batch get stream info", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
		mixStream, err = redisdao.BatchGetLiveSpeed(channelStrList)
		if err != nil {
			yylog.Error("cannot batch get live speed", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		var channels []Channel
		for _, c := range channelList {
			channels = append(channels, *c)
		}
		passwordMap = batchSubChannelPasswordMapInfo(channels)
	}).Wait()

	// channelFightList, videoFightChannelList, videoDatingChannelList, _ := channelListCategory(channelList, gameInfo, templateIDMap)

	// 交友房间
	var jyChannels []string
	for _, c := range channelList {
		templateID, _ := templateIDMap[toChannelStr(c.Sid, c.Ssid)]
		if templateID != getTemplateID(businessJY) {
			continue
		}
		jyChannels = append(jyChannels, toChannelStr(c.Sid, c.Ssid))
	}

	var vdGuest, vfGuest map[Channel][]int64
	var cfVsMap, vfVsMap map[Channel]match
	babyOnlineInfoMap := make(map[string]int64)
	var guestInfoMap map[string]string
	var roomBgMap map[string]TfBackground

	wg.Wrap(func() {
		vdGuest, vfGuest, cfVsMap, vfVsMap, err = batchGetVsAndGuestInfo(c.Request.Context(), channelList, gameInfo, templateIDMap)
		if err != nil {
			yylog.Error("cannot get vs and guest info", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		babyOnlineInfoMap, err = redisdao.GetBabyOnlineInfo()
		if err != nil {
			yylog.Error("cannot get baby online info", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
			ret.SetStatus(fts.StatusServerError)
			return
		}
	}).Wrap(func() {
		if guestInfoMap, err = redisdao.BatchGetGuestInfo(jyChannels); err != nil {
			yylog.Error("failed get guest info", fts.TraceID(ctx), fts.UID(uid), zap.Error(err))
		}
	}).Wrap(func() {
		roomBgMap = privilegedao.GetBackgroundMapWithCache(jyChannels...)
	}).Wait()

	for _, channel := range channelList {
		c := *channel
		var item viewItem
		item.Sid = c.Sid
		item.Ssid = c.Ssid
		item.TemplateID, _ = templateIDMap[toChannelStr(c.Sid, c.Ssid)]
		item.LiveInfo, _ = stream[uint64(c.Ssid)]
		item.MixStream, _ = mixStream[toChannelStr(c.Sid, c.Ssid)]
		item.Business = getBusiness(item.TemplateID, 0)
		item.Lock, _ = passwordMap[c]
		switch item.Business {
		case businessJY:
			info, _ := gameInfo[c]
			if info != nil {
				item.GameType = int(info.GetGameType())
				item.UID = info.CompereUid
				var vs match
				switch item.GameType {
				case constinfo.KChannelFight:
					vs, _ = cfVsMap[c]
				case constinfo.KVideoFightMatch, constinfo.KVideoFightMatchNew:
					vs, _ = vfVsMap[c]
					item.GuestUID = vfGuest[c]
					item.Match.GuestUID = vfGuest[Channel{Sid: vs.Sid, Ssid: vs.Ssid}]
				case constinfo.KVideoDating, constinfo.KVideoPk, constinfo.KVideoDatingNew, constinfo.KVideoPkNew:
					item.GuestUID = vdGuest[c]
				}
				channelStr := toChannelStr(c.Sid, c.Ssid)
				item.Background = roomBgMap[channelStr].AppUrl // 频道背景
				info, _ := guestInfoMap[channelStr]
				if len(info) > 0 {
					var guest []guestInfo
					if err := json.Unmarshal([]byte(guestInfoMap[channelStr]), &guest); err != nil {
						yylog.Error("failed unmarshal json", fts.TraceID(ctx), fts.UID(uid), zap.String("guestInfo", info))
					}
					item.OnlineInfo = guest
				}
				item.Match.Sid = vs.Sid
				item.Match.Ssid = vs.Ssid
				item.Match.UID = vs.UID
			}
		case businessBaby:
			item.UID, _ = babyOnlineInfoMap[toChannelStr(item.Sid, item.Ssid)]
		default:
		}
		yylog.Debug("recommend info", fts.TraceID(ctx), fts.UID(uid), fts.SID(item.Sid), fts.SSID(item.Ssid),
			zap.Any("item", item))
		ret.Items = append(ret.Items, &item)
	}
	ret.Interval = 10
	ret.Size = 20
	yylog.Info("recommend info", fts.TraceID(ctx),
		fts.UID(uid), zap.Int("itemSize", len(ret.Items)), zap.Any("timeElapsed", time.Since(now)))
}

// GetChannelFightVsInfo 获取乱斗对方主持信息
// @Summary 获取乱斗对方主持信息息
// @Description 获取乱斗对方主持信息
// @Tags 乱斗对方主持信息接口
// @Accept json
// @Produce json
// @Param sid query int true "频道"
// @Param ssid query int true "子频道"
// @Success 200 {object} getChannelFightVsInfoResp
// @Router /get_channel_fight_vs_info [get]
func GetChannelFightVsInfo(c *gin.Context) {
	var ret getChannelFightVsInfoResp
	defer c.JSON(http.StatusOK, &ret)

	var query getChannelFightVsInfoReq
	if err := c.ShouldBindQuery(&query); err != nil {
		ret.SetStatus(fts.StatusBadRequest)
		return
	}

	resp, err := channelfightdao.GetChannelFightVsInfo(query.Sid, query.Ssid)
	if err != nil {
		ret.SetStatus(fts.StatusServerError)
		return
	}
	if resp == nil || resp.VsCompere == 0 {
		return
	}
	webdbMap, err := webdbdao.BatchGetYYNickAndAvatar(resp.VsCompere)
	if err != nil {
		yylog.Error("cannot batch get yy nick and avatar", zap.Error(err))
		return
	}
	ret.UID = resp.VsCompere
	if len(webdbMap) > 0 {
		personal, _ := webdbMap[resp.VsCompere]
		ret.Nick = personal.Nick
		ret.Avatar = personal.HdLogo
	}
	yylog.Debug("get channel fight vs info", zap.Any("query", query), zap.Any("ret", ret))
}
